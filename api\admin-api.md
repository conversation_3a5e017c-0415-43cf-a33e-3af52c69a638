# 管理员 API 文档

## 概述

管理员API提供充值码管理、用户管理等高级功能，需要管理员权限。

**Base URL**: `/api/admin`

## 认证

所有API都需要JWT认证和管理员权限，请在请求头中包含：
```
Authorization: Bearer <admin-jwt-token>
```

## 充值码管理 API

### Base URL: `/api/admin/recharge-code`

### 1. 生成单个充值码

管理员生成单个充值码。

**请求**
```http
POST /api/admin/recharge-code/generate
Content-Type: application/x-www-form-urlencoded

pointsAmount=1000&amount=10.00&expireDays=30&remark=测试充值码
```

**参数**
- `pointsAmount`: 积分数量（必填）
- `amount`: 充值金额（可选）
- `expireDays`: 过期天数，默认30天
- `remark`: 备注信息（可选）

**响应**
```json
{
  "code": 200,
  "message": "充值码生成成功",
  "data": {
    "id": 1,
    "code": "dGVzdGNvZGUxMjM0NTY3OA==",
    "originalCode": "RC20250804ABCD1234",
    "pointsAmount": 1000,
    "amount": 10.00,
    "status": 0,
    "expireTime": "2025-09-03T15:30:00",
    "batchNo": "BATCH_20250804153000",
    "creatorUsername": "admin",
    "remark": "测试充值码",
    "createTime": "2025-08-04T15:30:00"
  }
}
```

### 2. 批量生成充值码

管理员批量生成充值码。

**请求**
```http
POST /api/admin/recharge-code/batch-generate
Content-Type: application/x-www-form-urlencoded

count=10&pointsAmount=500&amount=5.00&expireDays=30&remark=批量测试充值码
```

**参数**
- `count`: 生成数量（必填，最多100个）
- `pointsAmount`: 积分数量（必填）
- `amount`: 充值金额（可选）
- `expireDays`: 过期天数，默认30天
- `remark`: 备注信息（可选）

**响应**
```json
{
  "code": 200,
  "message": "批量生成充值码成功",
  "data": [
    {
      "id": 2,
      "originalCode": "RC20250804EFGH5678",
      "pointsAmount": 500,
      "amount": 5.00,
      "batchNo": "BATCH_20250804153000"
    }
    // ... 更多充值码
  ]
}
```

### 3. 查询充值码列表

分页查询充值码列表，支持筛选。

**请求**
```http
GET /api/admin/recharge-code/list?current=1&size=10&status=0&batchNo=BATCH_20250804153000
```

**参数**
- `current`: 页码，默认1
- `size`: 每页大小，默认10
- `status`: 状态筛选（可选）
- `batchNo`: 批次号筛选（可选）
- `creatorUsername`: 创建者筛选（可选）

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "pages": 3,
    "records": [
      {
        "id": 1,
        "code": "dGVzdGNvZGUxMjM0NTY3OA==",
        "pointsAmount": 1000,
        "amount": 10.00,
        "status": 0,
        "expireTime": "2025-09-03T15:30:00",
        "batchNo": "BATCH_20250804153000",
        "creatorUsername": "admin",
        "createTime": "2025-08-04T15:30:00"
      }
    ]
  }
}
```

### 4. 查询充值码详情

根据ID查询充值码详情。

**请求**
```http
GET /api/admin/recharge-code/{id}
```

**路径参数**
- `id`: 充值码ID

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "code": "dGVzdGNvZGUxMjM0NTY3OA==",
    "pointsAmount": 1000,
    "amount": 10.00,
    "status": 0,
    "usedUserId": null,
    "usedTime": null,
    "expireTime": "2025-09-03T15:30:00",
    "batchNo": "BATCH_20250804153000",
    "creatorId": 1,
    "creatorUsername": "admin",
    "remark": "测试充值码",
    "createTime": "2025-08-04T15:30:00"
  }
}
```

### 5. 更新过期充值码

批量更新过期充值码状态。

**请求**
```http
POST /api/admin/recharge-code/update-expired
```

**响应**
```json
{
  "code": 200,
  "message": "更新完成，共处理 5 个过期充值码",
  "data": 5
}
```

### 6. 删除充值码

逻辑删除指定的充值码。

**请求**
```http
DELETE /api/admin/recharge-code/{id}
```

**路径参数**
- `id`: 充值码ID

**响应**
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 7. 获取充值码统计

获取充值码的统计信息。

**请求**
```http
GET /api/admin/recharge-code/statistics
```

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalCount": 100,
    "unusedCount": 75,
    "usedCount": 20,
    "expiredCount": 5,
    "usageRate": "20.00%"
  }
}
```

## 用户管理 API

### Base URL: `/api/user-center`

### 管理员直充

管理员为指定用户直接充值积分。

**请求**
```http
POST /api/user-center/admin-recharge
Content-Type: application/json

{
  "userId": 2,
  "pointsAmount": 500,
  "remark": "管理员手动充值"
}
```

**参数**
- `userId`: 目标用户ID（必填）
- `pointsAmount`: 充值积分数量（必填）
- `remark`: 备注信息（可选）

**响应**
```json
{
  "code": 200,
  "message": "充值成功",
  "data": {
    "id": 3,
    "userId": 2,
    "rechargeType": 2,
    "rechargeTypeDesc": "管理员直充",
    "pointsAmount": 500,
    "pointsBefore": 100,
    "pointsAfter": 600,
    "operatorUsername": "admin",
    "status": 1,
    "statusDesc": "成功",
    "remark": "管理员手动充值",
    "createTime": "2025-08-04T16:00:00"
  }
}
```

## 权限说明

### 管理员权限要求
- `admin:read`: 查询权限
- `admin:write`: 操作权限

### 权限检查
所有管理员API都会检查以下条件：
1. JWT Token有效性
2. 用户是否具有管理员角色
3. 是否具有对应的操作权限

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 数据字典

### 充值码状态
- `0`: 未使用
- `1`: 已使用
- `2`: 已过期

### 充值类型
- `1`: 充值码充值
- `2`: 管理员直充

## 注意事项

1. **批量生成限制**: 单次最多生成100个充值码
2. **权限控制**: 所有操作都需要管理员权限
3. **数据安全**: 充值码采用加密存储
4. **操作记录**: 所有操作都会记录操作员信息
5. **过期处理**: 系统会自动处理过期充值码
