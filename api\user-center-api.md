# 用户中心 API 文档

## 概述

用户中心API提供用户账号管理、积分查看、密码修改、充值记录查询等功能。

**Base URL**: `/api/user-center`

## 认证

所有API都需要JWT认证，请在请求头中包含：
```
Authorization: Bearer <your-jwt-token>
```

## API接口

### 1. 获取用户中心信息

获取当前用户的账号信息、积分余额等。

**请求**
```http
GET /api/user-center/info
```

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "realName": "测试用户",
    "status": 1,
    "statusDesc": "正常",
    "points": 100,
    "createTime": "2025-01-01T10:00:00",
    "updateTime": "2025-08-04T15:30:00"
  }
}
```

### 2. 修改密码

修改当前用户的登录密码。

**请求**
```http
POST /api/user-center/change-password
Content-Type: application/json

{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456",
  "confirmPassword": "newPassword456"
}
```

**响应**
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

### 3. 获取充值记录

分页获取当前用户的积分充值记录。

**请求**
```http
GET /api/user-center/recharge-records?current=1&size=10
```

**参数**
- `current`: 页码，默认1
- `size`: 每页大小，默认10

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "pages": 3,
    "records": [
      {
        "id": 1,
        "rechargeType": 1,
        "rechargeTypeDesc": "充值码充值",
        "rechargeCodeMasked": "RC20****1234",
        "pointsAmount": 1000,
        "pointsBefore": 100,
        "pointsAfter": 1100,
        "amount": 10.00,
        "status": 1,
        "statusDesc": "成功",
        "createTime": "2025-08-04T10:30:00"
      }
    ]
  }
}
```

### 4. 获取消费记录

分页获取当前用户的积分消费记录。

**请求**
```http
GET /api/user-center/consumption-records?current=1&size=10
```

**参数**
- `current`: 页码，默认1
- `size`: 每页大小，默认10

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 15,
    "pages": 2,
    "records": [
      {
        "id": 1,
        "consumptionType": 1,
        "consumptionTypeDesc": "面试功能",
        "pointsAmount": 10,
        "pointsBefore": 110,
        "pointsAfter": 100,
        "businessId": "INTERVIEW_123456",
        "businessDesc": "面试会话",
        "status": 1,
        "statusDesc": "成功",
        "createTime": "2025-08-04T11:00:00"
      }
    ]
  }
}
```

### 5. 充值码充值

使用充值码为当前用户充值积分。

**请求**
```http
POST /api/user-center/recharge
Content-Type: application/json

{
  "rechargeCode": "RC20250804ABCD1234"
}
```

**响应**
```json
{
  "code": 200,
  "message": "充值成功",
  "data": {
    "id": 2,
    "rechargeType": 1,
    "rechargeTypeDesc": "充值码充值",
    "pointsAmount": 1000,
    "pointsBefore": 100,
    "pointsAfter": 1100,
    "amount": 10.00,
    "status": 1,
    "statusDesc": "成功",
    "createTime": "2025-08-04T12:00:00"
  }
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 数据字典

### 用户状态
- `0`: 禁用
- `1`: 正常

### 充值类型
- `1`: 充值码充值
- `2`: 管理员直充

### 消费类型
- `1`: 面试功能
- `2`: 语音识别
- `3`: AI答复
- `4`: 其他

### 状态码
- `0`: 失败
- `1`: 成功
- `2`: 处理中/退款
