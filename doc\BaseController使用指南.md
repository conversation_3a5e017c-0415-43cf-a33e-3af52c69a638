# BaseController 使用指南

## 概述

`BaseController` 是一个抽象基础控制器类，提供了统一的响应格式处理方法，避免在每个控制器中重复编写相同的响应代码。

## 主要功能

1. **统一响应格式**：所有API返回统一的JSON格式
2. **简化代码**：减少控制器中的重复代码
3. **错误处理**：提供标准化的错误响应
4. **灵活性**：支持ResponseEntity和Map两种返回类型

## 响应格式

所有API响应都遵循以下格式：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    }
}
```

## 使用方法

### 1. 继承BaseController

```java
@RestController
@RequestMapping("/api/example")
public class ExampleController extends BaseController {
    // 控制器方法
}
```

### 2. ResponseEntity返回类型

适用于需要设置HTTP状态码的场景：

```java
@GetMapping("/users")
public ResponseEntity<Map<String, Object>> getUsers() {
    try {
        List<User> users = userService.getAllUsers();
        return success("查询成功", users);
    } catch (Exception e) {
        return handleException(e, "查询用户失败");
    }
}
```

### 3. Map返回类型

适用于简单的JSON响应：

```java
@PostMapping("/login")
public Map<String, Object> login(@RequestBody LoginRequest request) {
    try {
        LoginResponse response = authService.login(request);
        return successMap("登录成功", response);
    } catch (Exception e) {
        return internalServerErrorMap("登录失败");
    }
}
```

## 可用方法

### 成功响应方法

#### ResponseEntity类型
- `success(Object data)` - 默认成功消息
- `success(String message, Object data)` - 自定义消息
- `success(String message)` - 仅消息，无数据

#### Map类型
- `successMap(Object data)` - 默认成功消息
- `successMap(String message, Object data)` - 自定义消息
- `successMap(String message)` - 仅消息，无数据

### 错误响应方法

#### ResponseEntity类型
- `error(int code, String message)` - 自定义错误码
- `badRequest(String message)` - 400错误
- `unauthorized(String message)` - 401未授权
- `forbidden(String message)` - 403禁止访问
- `notFound(String message)` - 404资源不存在
- `validationError(String message)` - 422参数验证失败
- `tooManyRequests(String message)` - 429请求过于频繁
- `internalServerError(String message)` - 500服务器错误

#### Map类型
- `errorMap(int code, String message)` - 自定义错误码
- `badRequestMap(String message)` - 400错误
- `internalServerErrorMap(String message)` - 500服务器错误

### 特殊方法

#### 异常处理
```java
protected ResponseEntity<Map<String, Object>> handleException(Exception e, String defaultMessage)
```

#### 分页响应
```java
protected ResponseEntity<Map<String, Object>> pageSuccess(Object data, long total, int current, int size)
```

## 使用示例

### 基本CRUD操作

```java
@RestController
@RequestMapping("/api/users")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    // 查询用户
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getUser(@PathVariable Long id) {
        try {
            User user = userService.getById(id);
            if (user == null) {
                return notFound("用户不存在");
            }
            return success(user);
        } catch (Exception e) {
            return handleException(e, "查询用户失败");
        }
    }

    // 创建用户
    @PostMapping
    public Map<String, Object> createUser(@RequestBody @Valid CreateUserRequest request) {
        try {
            User user = userService.create(request);
            return successMap("创建成功", user);
        } catch (IllegalArgumentException e) {
            return badRequestMap(e.getMessage());
        } catch (Exception e) {
            return internalServerErrorMap("创建用户失败");
        }
    }

    // 分页查询
    @GetMapping
    public ResponseEntity<Map<String, Object>> getUsers(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<User> page = userService.getPage(current, size);
            return pageSuccess(page.getRecords(), page.getTotal(), current, size);
        } catch (Exception e) {
            return handleException(e, "查询用户列表失败");
        }
    }
}
```

### 参数验证

```java
@PostMapping("/register")
public Map<String, Object> register(@RequestBody RegisterRequest request) {
    // 参数验证
    if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
        return badRequestMap("用户名不能为空");
    }
    
    if (request.getPassword() == null || request.getPassword().length() < 6) {
        return badRequestMap("密码长度不能少于6位");
    }
    
    try {
        User user = userService.register(request);
        return successMap("注册成功", user);
    } catch (Exception e) {
        return internalServerErrorMap("注册失败");
    }
}
```

## 最佳实践

1. **统一使用BaseController**：所有控制器都应该继承BaseController
2. **选择合适的返回类型**：需要HTTP状态码时用ResponseEntity，否则用Map
3. **合理使用错误方法**：根据具体错误类型选择合适的错误响应方法
4. **异常处理**：使用handleException方法统一处理异常
5. **参数验证**：在业务逻辑前进行参数验证，使用badRequest返回验证错误

## 迁移指南

### 从现有控制器迁移

1. **继承BaseController**
```java
// 修改前
public class UserController {

// 修改后  
public class UserController extends BaseController {
```

2. **替换响应代码**
```java
// 修改前
Map<String, Object> result = new HashMap<>();
result.put("code", 200);
result.put("message", "操作成功");
result.put("data", data);
return result;

// 修改后
return successMap("操作成功", data);
```

3. **替换错误处理**
```java
// 修改前
Map<String, Object> result = new HashMap<>();
result.put("code", 400);
result.put("message", "参数错误");
result.put("data", null);
return result;

// 修改后
return badRequestMap("参数错误");
```

通过使用BaseController，可以大大减少代码重复，提高开发效率，并确保API响应格式的一致性。
