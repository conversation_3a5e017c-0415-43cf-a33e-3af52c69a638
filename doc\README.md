# JobPlusV8 面试语音识别系统

## 项目概述

JobPlusV8 是一个基于 Spring Boot 的面试语音识别系统，集成了语音识别、用户认证、邮箱验证、Redis 缓存等功能。

## 技术栈

### 后端框架
- **Spring Boot 2.7.6**: 主框架
- **Spring Security**: 安全认证
- **MyBatis-Plus**: 数据持久化
- **MySQL**: 数据库
- **Redis**: 缓存系统

### 功能组件
- **JWT**: 无状态身份认证
- **邮件服务**: 验证码发送
- **语音识别**: 腾讯云语音服务
- **WebSocket**: 实时通信
- **Swagger**: API 文档

## 项目结构

```
jobplusv8/
├── src/main/java/cloud/ipanda/jobplusv8/
│   ├── config/          # 配置类
│   ├── controller/      # 控制器
│   ├── dto/            # 数据传输对象
│   ├── entity/         # 实体类
│   ├── mapper/         # MyBatis 映射器
│   ├── security/       # 安全相关
│   ├── service/        # 服务层
│   ├── util/           # 工具类
│   └── websocket/      # WebSocket 相关
├── src/main/resources/
│   ├── mapper/         # MyBatis XML 映射文件
│   ├── sql/           # 数据库初始化脚本
│   └── application.yml # 配置文件
├── src/test/          # 测试代码
└── doc/              # 项目文档
```

## 核心功能

### 🔐 用户认证系统
- JWT Token 认证
- BCrypt 密码加密
- 基于角色的权限控制 (RBAC)
- 邮箱验证码注册

### 📧 邮件服务
- 验证码发送（注册、登录、重置密码）
- 美观的 HTML 邮件模板
- 响应式设计，支持移动端
- 主题色彩区分不同操作类型
- 发送频率限制
- Redis 缓存验证码

### 💾 数据存储
- MySQL 主数据库
- Redis 缓存系统
- MyBatis-Plus ORM
- 数据库连接池 (Druid)

### 🎤 语音识别
- 腾讯云语音识别服务
- WebSocket 实时音频传输
- 面试语音转文字

### 📚 API 文档
- Swagger UI 接口文档
- 完整的 API 说明
- 在线测试功能

### 📋 审计日志
- 用户操作记录
- 异步日志处理
- 安全审计追踪
- 统计分析功能

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 3.0+

### 2. 数据库配置

```yaml
spring:
  datasource:
    url: *****************************************
    username: root
    password: lipanpan
```

### 3. Redis 配置

```yaml
spring:
  redis:
    host: *************
    port: 6379
    password: lipanpan
```

### 4. 初始化数据库

运行 `src/main/resources/sql/init.sql` 脚本初始化数据库表和测试数据。

### 5. 启动应用

```bash
mvn spring-boot:run
```

应用将在 http://localhost:80 启动。

## API 接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 用户管理
- `POST /api/user/register` - 用户注册
- `GET /api/user/{id}` - 获取用户信息
- `GET /api/user/page` - 分页查询用户

### 邮箱验证码
- `POST /api/email/send-code` - 发送验证码
- `POST /api/email/verify-code` - 验证验证码
- `GET /api/email/send-status` - 检查发送状态

### 邮件模板预览
- `GET /api/email-template/preview/register` - 预览注册邮件
- `GET /api/email-template/preview/login` - 预览登录邮件
- `GET /api/email-template/preview/reset` - 预览重置密码邮件

### 审计日志
- `GET /api/audit-log/page` - 分页查询审计日志
- `GET /api/audit-log/{id}` - 获取日志详情
- `GET /api/audit-log/operation-stats` - 操作统计
- `GET /api/audit-log/today-stats` - 今日统计

### 测试接口
- `GET /api/test/public` - 公开测试接口
- `GET /api/test/authenticated` - 需要认证的接口
- `GET /api/redis-test/connection` - Redis 连接测试

## 测试账号

- **管理员**: `admin` / `123456`
- **普通用户**: `test` / `123456`

## 文档说明

- [循环依赖修复文档](CIRCULAR_DEPENDENCY_FIX.md)
- [邮件配置指南](MAIL_CONFIG.md)
- [邮件模板设计](EMAIL_TEMPLATE.md)
- [审计日志系统](AUDIT_LOG.md)
- [控制器合并说明](CONTROLLER_MERGE.md)
- [帮助文档](HELP.md)

## 开发指南

### 权限控制

使用 `@PreAuthorize` 注解进行方法级权限控制：

```java
@PreAuthorize("hasAuthority('user:read')")
@GetMapping("/users")
public List<User> getUsers() {
    // 只有拥有 user:read 权限的用户才能访问
}
```

### Redis 缓存

使用 `RedisUtil` 工具类进行缓存操作：

```java
@Autowired
private RedisUtil redisUtil;

// 设置缓存
redisUtil.setString("key", "value", 300, TimeUnit.SECONDS);

// 获取缓存
String value = redisUtil.getString("key");
```

### 邮箱验证码

使用 `EmailVerificationService` 发送和验证邮箱验证码：

```java
@Autowired
private EmailVerificationService emailService;

// 发送验证码
boolean sent = emailService.sendVerificationCode("<EMAIL>", "register");

// 验证验证码
boolean valid = emailService.verifyCode("<EMAIL>", "123456", "register");
```

## 部署说明

### 1. 生产环境配置

- 修改数据库连接信息
- 配置真实的邮箱服务
- 设置安全的 JWT 密钥
- 配置 Redis 连接

### 2. 打包部署

```bash
mvn clean package
java -jar target/jobplusv8-0.0.1-SNAPSHOT.jar
```

## 故障排除

### 常见问题

1. **循环依赖错误**: 参考 [循环依赖修复文档](CIRCULAR_DEPENDENCY_FIX.md)
2. **数据库连接失败**: 检查数据库配置和网络连接
3. **Redis 连接失败**: 检查 Redis 服务状态和配置
4. **邮件发送失败**: 检查邮箱配置和授权码

### 日志查看

应用日志会记录详细的操作信息，包括：
- 用户认证过程
- 数据库操作
- Redis 缓存操作
- 邮件发送状态

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

---

**版本**: v0.0.1-SNAPSHOT  
**更新时间**: 2025年7月22日
