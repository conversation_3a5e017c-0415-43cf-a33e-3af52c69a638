package cloud.ipanda.jobplusv8.aspect;

import cloud.ipanda.jobplusv8.interceptor.PointsInterceptor;
import cloud.ipanda.jobplusv8.service.PointsService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 积分消费切面
 * 
 * 功能说明：
 * 1. 在业务方法执行成功后自动扣除积分
 * 2. 记录积分消费记录
 * 3. 处理业务失败时的积分退还
 * 4. 与积分拦截器配合使用
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Aspect
@Component
public class PointsConsumptionAspect {

    @Autowired
    private PointsService pointsService;

    /**
     * 环绕通知：处理需要消费积分的方法
     */
    @Around("execution(* cloud.ipanda.jobplusv8.controller.InterviewController.createSession(..)) || " +
            "execution(* cloud.ipanda.jobplusv8.controller.InterviewController.startInterview(..))")
    public Object handlePointsConsumption(ProceedingJoinPoint joinPoint) throws Throwable {
        log.debug("【积分消费切面】开始处理方法: {}", joinPoint.getSignature().getName());

        // 获取当前请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.debug("【积分消费切面】非Web请求，跳过积分处理");
            return joinPoint.proceed();
        }

        HttpServletRequest request = attributes.getRequest();
        
        // 获取积分消费规则（由拦截器设置）
        PointsInterceptor.PointsConsumptionRule rule = 
            (PointsInterceptor.PointsConsumptionRule) request.getAttribute("pointsConsumptionRule");
        Long userId = (Long) request.getAttribute("currentUserId");

        if (rule == null || userId == null) {
            log.debug("【积分消费切面】无积分消费规则或用户ID，跳过积分处理");
            return joinPoint.proceed();
        }

        log.info("【积分消费切面】准备消费积分，用户ID: {}, 功能: {}, 积分: {}", 
                userId, rule.getFunctionName(), rule.getPointsRequired());

        Object result = null;
        boolean businessSuccess = false;
        String businessId = null;

        try {
            // 执行业务方法
            result = joinPoint.proceed();
            businessSuccess = true;

            // 尝试从返回结果中获取业务ID
            businessId = extractBusinessId(result, joinPoint.getSignature().getName());

            log.info("【积分消费切面】业务执行成功，准备扣除积分");

        } catch (Exception e) {
            log.error("【积分消费切面】业务执行失败: {}", e.getMessage(), e);
            businessSuccess = false;
            throw e; // 重新抛出异常
        } finally {
            // 只有业务执行成功时才扣除积分
            if (businessSuccess) {
                try {
                    boolean consumeSuccess = pointsService.consumePoints(
                        userId,
                        rule.getPointsRequired(),
                        rule.getConsumptionType(),
                        businessId,
                        rule.getDescription()
                    );

                    if (consumeSuccess) {
                        log.info("【积分消费切面】积分扣除成功，用户ID: {}, 扣除积分: {}", 
                                userId, rule.getPointsRequired());
                    } else {
                        log.error("【积分消费切面】积分扣除失败，用户ID: {}, 积分: {}", 
                                userId, rule.getPointsRequired());
                        // 注意：这里不抛出异常，避免影响已经成功的业务操作
                        // 可以考虑发送告警或记录到特殊日志中
                    }

                } catch (Exception e) {
                    log.error("【积分消费切面】积分扣除异常，用户ID: {}, 错误: {}", 
                            userId, e.getMessage(), e);
                    // 同样不抛出异常
                }
            }
        }

        return result;
    }

    /**
     * 从业务方法返回结果中提取业务ID
     */
    private String extractBusinessId(Object result, String methodName) {
        if (result == null) {
            return null;
        }

        try {
            // 根据不同的方法名和返回类型提取业务ID
            if (methodName.contains("createSession") || methodName.contains("startInterview")) {
                // 面试相关方法，尝试从返回结果中提取会话ID
                if (result instanceof org.springframework.http.ResponseEntity) {
                    // 处理ResponseEntity类型的返回值
                    org.springframework.http.ResponseEntity<?> responseEntity = 
                        (org.springframework.http.ResponseEntity<?>) result;
                    Object body = responseEntity.getBody();
                    if (body instanceof java.util.Map) {
                        @SuppressWarnings("unchecked")
                        java.util.Map<String, Object> responseMap = (java.util.Map<String, Object>) body;
                        Object data = responseMap.get("data");
                        if (data instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) data;
                            Object sessionId = dataMap.get("sessionId");
                            return sessionId != null ? sessionId.toString() : null;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("【积分消费切面】提取业务ID失败: {}", e.getMessage());
        }

        // 如果无法提取具体的业务ID，使用时间戳作为标识
        return methodName + "_" + System.currentTimeMillis();
    }
}
