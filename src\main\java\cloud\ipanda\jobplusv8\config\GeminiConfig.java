package cloud.ipanda.jobplusv8.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * Gemini API配置类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Configuration
@ConfigurationProperties(prefix = "gemini")
@Data
public class GeminiConfig {

    /**
     * Gemini API基础URL
     */
    private String baseUrl = "http://*************:8000";

    /**
     * API密钥
     */
    private String apiKey = "sk-lipanpan";

    /**
     * 模型名称
     */
    private String model = "gemini-2.5-pro";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 温度参数
     */
    private double temperature = 1.0;

    /**
     * 响应MIME类型
     */
    private String responseMimeType = "text/plain";

    /**
     * 思考预算
     */
    private int thinkingBudget = 6656;

    /**
     * 配置RestTemplate Bean
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        return new RestTemplate(factory);
    }
}
