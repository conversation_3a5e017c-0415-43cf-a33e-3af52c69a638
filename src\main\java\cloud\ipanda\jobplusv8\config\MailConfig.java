package cloud.ipanda.jobplusv8.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 邮件服务配置类
 * 
 * 功能说明：
 * 1. 从application.yml配置文件中读取邮件服务配置信息
 * 2. 提供邮件发送所需的SMTP服务器配置
 * 3. 支持配置热更新和属性绑定
 * 4. 管理邮件模板和验证码相关配置
 * 
 * 配置项说明：
 * - host: SMTP服务器地址
 * - port: SMTP服务器端口
 * - username: 邮箱账号
 * - password: 邮箱密码或授权码
 * - from: 发件人邮箱地址
 * - fromName: 发件人显示名称
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@Data  // Lombok注解，自动生成getter、setter、toString等方法
@Component  // Spring组件注解，将此类注册为Spring Bean
@ConfigurationProperties(prefix = "spring.mail")  // 绑定配置文件中spring.mail前缀的属性
public class MailConfig {
    
    /**
     * SMTP服务器地址
     * 腾讯邮箱: smtp.qq.com
     * 网易邮箱: smtp.163.com
     * Gmail: smtp.gmail.com
     */
    private String host;
    
    /**
     * SMTP服务器端口
     * 通常为587(TLS)或465(SSL)
     */
    private Integer port;
    
    /**
     * 邮箱用户名（完整邮箱地址）
     */
    private String username;
    
    /**
     * 邮箱密码或授权码
     * 建议使用授权码而不是邮箱密码
     */
    private String password;
    
    /**
     * 发件人邮箱地址
     * 通常与username相同
     */
    private String from;
    
    /**
     * 发件人显示名称
     * 在收件人看到的发件人名称
     */
    private String fromName = "JobPlusV8面试系统";
    
    /**
     * 验证码有效期（分钟）
     */
    private Integer codeExpireMinutes = 5;
    
    /**
     * 验证码长度
     */
    private Integer codeLength = 6;
    
    /**
     * 同一邮箱发送验证码的间隔时间（秒）
     */
    private Integer sendIntervalSeconds = 60;
}
