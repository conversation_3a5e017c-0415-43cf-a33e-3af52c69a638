package cloud.ipanda.jobplusv8.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Swagger API文档配置类
 * 
 * 功能说明：
 * 1. 配置Swagger3 (OpenAPI 3.0) 文档生成
 * 2. 自定义API文档的基本信息和描述
 * 3. 设置API扫描范围和路径过滤规则
 * 4. 提供在线API测试界面
 * 
 * 访问地址：
 * - Swagger UI: http://localhost:80/swagger-ui/index.html
 * - API文档JSON: http://localhost:80/v3/api-docs
 * 
 * 技术特点：
 * - 基于注解自动生成API文档
 * - 支持在线API测试
 * - 实时更新，与代码同步
 * - 支持多种数据格式展示
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@Configuration  // Spring配置类注解
@EnableOpenApi  // 启用OpenAPI 3.0支持
public class SwaggerConfig {

    /**
     * 创建API文档配置Bean
     * 
     * 功能说明：
     * 1. 配置API文档的基本信息
     * 2. 设置API扫描的包路径
     * 3. 定义API路径的过滤规则
     * 4. 指定文档类型为OpenAPI 3.0
     * 
     * 扫描策略：
     * - 扫描指定包下的所有Controller
     * - 包含所有路径的API接口
     * - 排除错误页面和静态资源
     * 
     * @return Docket API文档配置对象
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)  // 使用OpenAPI 3.0
                .apiInfo(apiInfo())  // 设置API基本信息
                .select()
                // 扫描指定包下的Controller类
                .apis(RequestHandlerSelectors.basePackage("cloud.ipanda.jobplusv8"))
                // 包含所有路径的API
                .paths(PathSelectors.any())
                .build();
    }

    /**
     * 构建API基本信息
     * 
     * 功能说明：
     * 1. 设置API文档的标题和描述
     * 2. 配置版本信息和联系方式
     * 3. 添加服务条款和许可证信息
     * 
     * 信息内容：
     * - 项目名称和功能描述
     * - 当前版本号
     * - 开发团队联系方式
     * - 项目许可证信息
     * 
     * @return ApiInfo API基本信息对象
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                // API标题
                .title("JobPlusV8 面试语音识别系统 API文档")
                // API描述
                .description("基于Spring Boot和腾讯云语音识别的实时面试语音转文字系统\n\n" +
                           "主要功能：\n" +
                           "• 实时语音识别和转文字\n" +
                           "• WebSocket音频流传输\n" +
                           "• 面试过程记录和管理\n" +
                           "• 多种音频格式支持\n\n" +
                           "技术栈：\n" +
                           "• Spring Boot 2.7.6\n" +
                           "• 腾讯云语音识别SDK\n" +
                           "• WebSocket实时通信\n" +
                           "• Maven项目管理")
                // 服务条款URL
                .termsOfServiceUrl("https://github.com/your-repo/terms")
                // 联系信息
                .contact(new Contact(
                        "JobPlusV8开发团队",           // 联系人名称
                        "https://github.com/your-repo", // 联系人URL
                        "<EMAIL>"           // 联系人邮箱
                ))
                // 许可证信息
                .license("Apache License 2.0")
                .licenseUrl("https://www.apache.org/licenses/LICENSE-2.0")
                // API版本
                .version("v0.0.1-SNAPSHOT")
                .build();
    }
}
