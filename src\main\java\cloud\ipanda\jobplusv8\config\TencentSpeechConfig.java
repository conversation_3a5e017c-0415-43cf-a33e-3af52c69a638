package cloud.ipanda.jobplusv8.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 腾讯云语音识别服务配置类
 *
 * 功能说明：
 * 1. 从application.yml配置文件中读取腾讯云API配置信息
 * 2. 提供腾讯云语音识别服务所需的认证参数
 * 3. 支持配置热更新和属性绑定
 *
 * 配置项说明：
 * - secretId: 腾讯云API密钥ID，用于身份验证
 * - secretKey: 腾讯云API密钥Key，用于签名验证
 * - region: 腾讯云服务区域，如ap-beijing、ap-shanghai等
 * - appId: 腾讯云应用ID，标识具体的应用实例
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@Data  // Lombok注解，自动生成getter、setter、toString等方法
@Component  // Spring组件注解，将此类注册为Spring Bean
@ConfigurationProperties(prefix = "tencent.cloud")  // 绑定配置文件中tencent.cloud前缀的属性
public class TencentSpeechConfig {

    /**
     * 腾讯云API密钥ID
     * 用于标识API调用者身份，从腾讯云控制台获取
     */
    private String secretId;

    /**
     * 腾讯云API密钥Key
     * 用于API请求签名，确保请求安全性，从腾讯云控制台获取
     */
    private String secretKey;

    /**
     * 腾讯云服务区域
     * 指定语音识别服务的地理区域，影响服务延迟和数据存储位置
     * 常用值：ap-beijing(北京)、ap-shanghai(上海)、ap-guangzhou(广州)
     */
    private String region;

    /**
     * 腾讯云应用ID
     * 标识具体的应用实例，用于资源隔离和计费
     */
    private String appId;

    /**
     * 获取应用ID
     * @return 腾讯云应用ID
     */
    public String getAppId() {
        return appId;
    }

    /**
     * 获取API密钥ID
     * @return 腾讯云API密钥ID
     */
    public String getSecretId() {
        return secretId;
    }

    /**
     * 设置API密钥ID
     * @param secretId 腾讯云API密钥ID
     */
    public void setSecretId(String secretId) {
        this.secretId = secretId;
    }

    /**
     * 获取API密钥Key
     * @return 腾讯云API密钥Key
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * 设置API密钥Key
     * @param secretKey 腾讯云API密钥Key
     */
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    /**
     * 获取服务区域
     * @return 腾讯云服务区域
     */
    public String getRegion() {
        return region;
    }

    /**
     * 设置服务区域
     * @param region 腾讯云服务区域
     */
    public void setRegion(String region) {
        this.region = region;
    }

    /**
     * 设置应用ID
     * @param appId 腾讯云应用ID
     */
    public void setAppId(String appId) {
        this.appId = appId;
    }
}