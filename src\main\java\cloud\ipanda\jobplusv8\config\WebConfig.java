package cloud.ipanda.jobplusv8.config;

import cloud.ipanda.jobplusv8.interceptor.PointsInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * 功能说明：
 * 1. 配置Web相关的拦截器
 * 2. 注册积分拦截器
 * 3. 设置拦截器的拦截路径和排除路径
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private PointsInterceptor pointsInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("【Web配置】注册积分拦截器");
        
        registry.addInterceptor(pointsInterceptor)
                // 拦截需要消费积分的API路径
                .addPathPatterns(
                    "/api/interview/**",     // 面试功能
                    "/api/speech/**",        // 语音识别功能
                    "/api/ai/**"             // AI答复功能
                )
                // 排除不需要积分检查的路径
                .excludePathPatterns(
                    "/api/interview/health",           // 健康检查
                    "/api/speech/duration/**",         // 时长查询
                    "/api/ai/health",                  // AI服务健康检查
                    "/api/user-center/**",             // 用户中心（包含充值功能）
                    "/api/auth/**",                    // 认证相关
                    "/api/email/**",                   // 邮件服务
                    "/api/audit-log/**",               // 审计日志
                    "/api/resume/**",                  // 简历管理
                    "/swagger-ui.html",                // Swagger文档
                    "/swagger-resources/**",           // Swagger资源
                    "/v2/api-docs",                    // API文档
                    "/webjars/**",                     // 静态资源
                    "/error",                          // 错误页面
                    "/favicon.ico"                     // 图标
                )
                // 设置拦截器顺序（数字越小优先级越高）
                .order(100);
        
        log.info("【Web配置】积分拦截器注册完成");
    }
}
