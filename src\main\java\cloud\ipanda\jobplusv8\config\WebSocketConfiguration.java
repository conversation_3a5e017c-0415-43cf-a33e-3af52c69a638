package cloud.ipanda.jobplusv8.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 *
 * 功能说明：
 * 1. 启用WebSocket支持，允许应用程序处理WebSocket连接
 * 2. 配置ServerEndpointExporter，自动注册WebSocket端点
 * 3. 支持基于注解的WebSocket端点声明(@ServerEndpoint)
 *
 * 技术细节：
 * - 使用Java标准的WebSocket API (JSR-356)
 * - 支持嵌入式Servlet容器(如Tomcat)中的WebSocket
 * - 自动扫描并注册@ServerEndpoint注解的类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@Configuration    // Spring配置类注解，标识这是一个配置类
@EnableWebSocket  // 启用WebSocket支持
public class WebSocketConfiguration {

    /**
     * 注册ServerEndpointExporter Bean
     *
     * 功能说明：
     * 1. ServerEndpointExporter会自动扫描并注册使用@ServerEndpoint注解的WebSocket端点
     * 2. 在嵌入式Servlet容器中运行时必须配置此Bean
     * 3. 如果部署到外部Servlet容器，则不需要此Bean
     *
     * 工作原理：
     * - Spring Boot启动时，ServerEndpointExporter会扫描所有@ServerEndpoint注解的类
     * - 将这些类注册到WebSocket容器中，使其能够处理WebSocket连接
     * - 支持依赖注入，允许WebSocket端点使用Spring管理的Bean
     *
     * @return ServerEndpointExporter实例，用于注册WebSocket端点
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}