package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.entity.RechargeCode;
import cloud.ipanda.jobplusv8.service.RechargeCodeService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员充值码管理控制器
 * 
 * 功能说明：
 * 1. 管理员生成充值码
 * 2. 查询充值码列表
 * 3. 管理充值码状态
 * 4. 批量操作充值码
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Api(tags = "管理员-充值码管理", description = "管理员充值码管理相关API")
@RestController
@RequestMapping("/api/admin/recharge-code")
@PreAuthorize("hasAuthority('admin:write')")
public class AdminRechargeCodeController extends BaseController {

    @Autowired
    private RechargeCodeService rechargeCodeService;

    @Autowired
    private JwtUtil jwtUtil;

    @ApiOperation(value = "生成单个充值码", notes = "管理员生成单个充值码")
    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generateRechargeCode(
            @ApiParam(value = "积分数量", required = true) @RequestParam @NotNull @Min(1) Long pointsAmount,
            @ApiParam(value = "充值金额") @RequestParam(required = false) BigDecimal amount,
            @ApiParam(value = "过期天数") @RequestParam(defaultValue = "30") Integer expireDays,
            @ApiParam(value = "备注") @RequestParam(required = false) String remark,
            HttpServletRequest request) {
        
        try {
            Long creatorId = getCurrentUserId(request);
            String creatorUsername = getCurrentUsername(request);
            
            LocalDateTime expireTime = LocalDateTime.now().plusDays(expireDays);
            
            RechargeCode rechargeCode = rechargeCodeService.generateRechargeCode(
                pointsAmount, amount, expireTime, creatorId, creatorUsername, remark);
            
            log.info("【生成充值码成功】创建者: {}, 积分: {}, 原始码: {}", 
                    creatorUsername, pointsAmount, rechargeCode.getOriginalCode());
            
            return success("充值码生成成功", rechargeCode);
            
        } catch (Exception e) {
            log.error("【生成充值码失败】错误: {}", e.getMessage(), e);
            return handleException(e, "生成充值码失败");
        }
    }

    @ApiOperation(value = "批量生成充值码", notes = "管理员批量生成充值码")
    @PostMapping("/batch-generate")
    public ResponseEntity<Map<String, Object>> batchGenerateRechargeCodes(
            @ApiParam(value = "生成数量", required = true) @RequestParam @NotNull @Min(1) Integer count,
            @ApiParam(value = "积分数量", required = true) @RequestParam @NotNull @Min(1) Long pointsAmount,
            @ApiParam(value = "充值金额") @RequestParam(required = false) BigDecimal amount,
            @ApiParam(value = "过期天数") @RequestParam(defaultValue = "30") Integer expireDays,
            @ApiParam(value = "备注") @RequestParam(required = false) String remark,
            HttpServletRequest request) {
        
        try {
            if (count > 100) {
                return badRequest("单次最多生成100个充值码");
            }
            
            Long creatorId = getCurrentUserId(request);
            String creatorUsername = getCurrentUsername(request);
            
            LocalDateTime expireTime = LocalDateTime.now().plusDays(expireDays);
            
            List<RechargeCode> rechargeCodes = rechargeCodeService.batchGenerateRechargeCodes(
                count, pointsAmount, amount, expireTime, creatorId, creatorUsername, remark);
            
            log.info("【批量生成充值码成功】创建者: {}, 数量: {}, 积分: {}", 
                    creatorUsername, count, pointsAmount);
            
            return success("批量生成充值码成功", rechargeCodes);
            
        } catch (Exception e) {
            log.error("【批量生成充值码失败】错误: {}", e.getMessage(), e);
            return handleException(e, "批量生成充值码失败");
        }
    }

    @ApiOperation(value = "查询充值码列表", notes = "分页查询充值码列表")
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('admin:read')")
    public ResponseEntity<Map<String, Object>> getRechargeCodeList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam(value = "状态筛选") @RequestParam(required = false) Integer status,
            @ApiParam(value = "批次号") @RequestParam(required = false) String batchNo,
            @ApiParam(value = "创建者") @RequestParam(required = false) String creatorUsername) {
        
        try {
            Page<RechargeCode> page = new Page<>(current, size);
            QueryWrapper<RechargeCode> queryWrapper = new QueryWrapper<>();
            
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            if (batchNo != null && !batchNo.trim().isEmpty()) {
                queryWrapper.like("batch_no", batchNo.trim());
            }
            if (creatorUsername != null && !creatorUsername.trim().isEmpty()) {
                queryWrapper.like("creator_username", creatorUsername.trim());
            }
            
            queryWrapper.orderByDesc("create_time");
            
            Page<RechargeCode> resultPage = rechargeCodeService.page(page, queryWrapper);
            
            return success("查询成功", resultPage);
            
        } catch (Exception e) {
            log.error("【查询充值码列表失败】错误: {}", e.getMessage(), e);
            return handleException(e, "查询充值码列表失败");
        }
    }

    @ApiOperation(value = "查询充值码详情", notes = "根据ID查询充值码详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('admin:read')")
    public ResponseEntity<Map<String, Object>> getRechargeCodeById(
            @ApiParam(value = "充值码ID", required = true) @PathVariable Long id) {
        
        try {
            RechargeCode rechargeCode = rechargeCodeService.getById(id);
            
            if (rechargeCode == null) {
                return notFound("充值码不存在");
            }
            
            return success("查询成功", rechargeCode);
            
        } catch (Exception e) {
            log.error("【查询充值码详情失败】ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "查询充值码详情失败");
        }
    }

    @ApiOperation(value = "更新过期充值码", notes = "批量更新过期充值码状态")
    @PostMapping("/update-expired")
    public ResponseEntity<Map<String, Object>> updateExpiredCodes() {
        try {
            int count = rechargeCodeService.updateExpiredCodes();
            
            log.info("【更新过期充值码】数量: {}", count);
            return success("更新完成，共处理 " + count + " 个过期充值码", count);
            
        } catch (Exception e) {
            log.error("【更新过期充值码失败】错误: {}", e.getMessage(), e);
            return handleException(e, "更新过期充值码失败");
        }
    }

    @ApiOperation(value = "删除充值码", notes = "逻辑删除指定的充值码")
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteRechargeCode(
            @ApiParam(value = "充值码ID", required = true) @PathVariable Long id,
            HttpServletRequest request) {
        
        try {
            RechargeCode rechargeCode = rechargeCodeService.getById(id);
            if (rechargeCode == null) {
                return notFound("充值码不存在");
            }
            
            if (rechargeCode.getStatus() == RechargeCode.Status.USED) {
                return badRequest("已使用的充值码不能删除");
            }
            
            boolean success = rechargeCodeService.removeById(id);
            
            if (success) {
                String operatorUsername = getCurrentUsername(request);
                log.info("【删除充值码成功】操作员: {}, 充值码ID: {}", operatorUsername, id);
                return success("删除成功", null);
            } else {
                return error(500, "删除失败");
            }
            
        } catch (Exception e) {
            log.error("【删除充值码失败】ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "删除充值码失败");
        }
    }

    @ApiOperation(value = "获取充值码统计", notes = "获取充值码的统计信息")
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('admin:read')")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            QueryWrapper<RechargeCode> queryWrapper = new QueryWrapper<>();
            
            // 总数
            long totalCount = rechargeCodeService.count();
            
            // 未使用数量
            queryWrapper.clear();
            queryWrapper.eq("status", RechargeCode.Status.UNUSED);
            long unusedCount = rechargeCodeService.count(queryWrapper);
            
            // 已使用数量
            queryWrapper.clear();
            queryWrapper.eq("status", RechargeCode.Status.USED);
            long usedCount = rechargeCodeService.count(queryWrapper);
            
            // 已过期数量
            queryWrapper.clear();
            queryWrapper.eq("status", RechargeCode.Status.EXPIRED);
            long expiredCount = rechargeCodeService.count(queryWrapper);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", totalCount);
            statistics.put("unusedCount", unusedCount);
            statistics.put("usedCount", usedCount);
            statistics.put("expiredCount", expiredCount);
            statistics.put("usageRate", totalCount > 0 ? String.format("%.2f%%", (double) usedCount / totalCount * 100) : "0%");
            
            return success("查询成功", statistics);
            
        } catch (Exception e) {
            log.error("【获取充值码统计失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取统计信息失败");
        }
    }

    /**
     * 从请求中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        return jwtUtil.getCurrentUserIdFromRequest(request);
    }

    /**
     * 从请求中获取当前用户名
     */
    private String getCurrentUsername(HttpServletRequest request) {
        try {
            String token = jwtUtil.getTokenFromRequest(request);
            if (token != null) {
                return jwtUtil.getUsernameFromToken(token);
            }
        } catch (Exception e) {
            log.error("从请求中获取用户名失败", e);
        }
        return null;
    }
}
