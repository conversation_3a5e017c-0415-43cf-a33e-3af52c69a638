package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.entity.AuditLog;
import cloud.ipanda.jobplusv8.service.AuditLogService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 审计日志控制器
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Api(tags = "审计日志管理", description = "审计日志查询和管理相关API")
@RestController
@RequestMapping("/api/audit-log")
public class AuditLogController extends BaseController {

    @Autowired
    private AuditLogService auditLogService;

    @ApiOperation(value = "分页查询审计日志", notes = "支持按用户名、操作类型、时间范围等条件查询")
    @PreAuthorize("hasAuthority('user:read')")
    @GetMapping("/page")
    public Map<String, Object> getAuditLogPage(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam(value = "用户名") @RequestParam(required = false) String username,
            @ApiParam(value = "操作类型") @RequestParam(required = false) String operationType,
            @ApiParam(value = "操作状态：0-失败，1-成功") @RequestParam(required = false) Integer status,
            @ApiParam(value = "客户端IP") @RequestParam(required = false) String clientIp,
            @ApiParam(value = "开始时间", example = "2025-07-22 00:00:00") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间", example = "2025-07-22 23:59:59") @RequestParam(required = false) String endTime) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Page<AuditLog> page = new Page<>(current, size);
            QueryWrapper<AuditLog> queryWrapper = new QueryWrapper<>();
            
            // 构建查询条件
            if (username != null && !username.trim().isEmpty()) {
                queryWrapper.like("username", username);
            }
            if (operationType != null && !operationType.trim().isEmpty()) {
                queryWrapper.eq("operation_type", operationType);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            if (clientIp != null && !clientIp.trim().isEmpty()) {
                queryWrapper.like("client_ip", clientIp);
            }
            if (startTime != null && !startTime.trim().isEmpty()) {
                queryWrapper.ge("operation_time", LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                queryWrapper.le("operation_time", LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            
            // 按操作时间倒序排列
            queryWrapper.orderByDesc("operation_time");
            
            Page<AuditLog> auditLogPage = auditLogService.page(page, queryWrapper);

            result = successMap("查询成功", auditLogPage);

        } catch (Exception e) {
            log.error("分页查询审计日志异常", e);
            result = internalServerErrorMap("系统异常");
        }
        return result;
    }

    @ApiOperation(value = "获取审计日志详情", notes = "根据ID获取审计日志的详细信息")
    @PreAuthorize("hasAuthority('user:read')")
    @GetMapping("/{id}")
    public Map<String, Object> getAuditLogById(@ApiParam(value = "日志ID", required = true) @PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            AuditLog auditLog = auditLogService.getById(id);
            if (auditLog != null) {
                result = successMap("获取成功", auditLog);
            } else {
                result = errorMap(404, "审计日志不存在");
            }
        } catch (Exception e) {
            log.error("获取审计日志详情异常", e);
            result = internalServerErrorMap("系统异常");
        }
        return result;
    }

    @ApiOperation(value = "获取操作类型统计", notes = "统计各种操作类型的数量")
    @PreAuthorize("hasAuthority('user:read')")
    @GetMapping("/operation-stats")
    public Map<String, Object> getOperationStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            QueryWrapper<AuditLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("operation_type, COUNT(*) as count")
                       .groupBy("operation_type")
                       .orderByDesc("count");
            
            // 这里简化处理，实际项目中可能需要更复杂的统计逻辑
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalLogs", auditLogService.count());
            stats.put("loginCount", auditLogService.count(new QueryWrapper<AuditLog>().eq("operation_type", "USER_LOGIN")));
            stats.put("registerCount", auditLogService.count(new QueryWrapper<AuditLog>().eq("operation_type", "USER_REGISTER")));
            stats.put("emailCodeCount", auditLogService.count(new QueryWrapper<AuditLog>().eq("operation_type", "EMAIL_CODE_SEND")));
            stats.put("emailVerifyCount", auditLogService.count(new QueryWrapper<AuditLog>().eq("operation_type", "EMAIL_CODE_VERIFY")));

            result = successMap("统计成功", stats);

        } catch (Exception e) {
            log.error("获取操作类型统计异常", e);
            result = internalServerErrorMap("系统异常");
        }
        return result;
    }

    @ApiOperation(value = "获取今日操作统计", notes = "获取今日各种操作的统计数据")
    @PreAuthorize("hasAuthority('user:read')")
    @GetMapping("/today-stats")
    public Map<String, Object> getTodayStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(999999999);
            
            QueryWrapper<AuditLog> todayWrapper = new QueryWrapper<AuditLog>()
                    .between("operation_time", todayStart, todayEnd);
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("todayTotal", auditLogService.count(todayWrapper));
            stats.put("todayLogin", auditLogService.count(todayWrapper.clone().eq("operation_type", "USER_LOGIN")));
            stats.put("todayRegister", auditLogService.count(todayWrapper.clone().eq("operation_type", "USER_REGISTER")));
            stats.put("todayEmailCode", auditLogService.count(todayWrapper.clone().eq("operation_type", "EMAIL_CODE_SEND")));
            stats.put("todaySuccess", auditLogService.count(todayWrapper.clone().eq("status", 1)));
            stats.put("todayFailed", auditLogService.count(todayWrapper.clone().eq("status", 0)));

            result = successMap("统计成功", stats);

        } catch (Exception e) {
            log.error("获取今日操作统计异常", e);
            result = internalServerErrorMap("系统异常");
        }
        return result;
    }
}
