package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.dto.LoginRequest;
import cloud.ipanda.jobplusv8.dto.LoginResponse;
import cloud.ipanda.jobplusv8.dto.RegisterRequest;
import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.security.CustomUserDetails;
import cloud.ipanda.jobplusv8.service.AuditLogService;
import cloud.ipanda.jobplusv8.service.EmailVerificationService;
import cloud.ipanda.jobplusv8.service.UserService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Api(tags = "用户认证")
@RestController
@RequestMapping("/api/auth")
public class AuthController extends BaseController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private AuditLogService auditLogService;

    @Autowired
    private EmailVerificationService emailVerificationService;

    @ApiOperation("用户注册")
    @PostMapping("/register")
    public Map<String, Object> register(@Valid @RequestBody RegisterRequest request, HttpServletRequest httpRequest) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        String errorMsg = null;
        boolean success = false;

        try {
            // 验证邮箱验证码
            boolean codeValid = emailVerificationService.verifyCode(
                request.getEmail(),
                request.getEmailCode(),
                "register"
            );

            if (!codeValid) {
                errorMsg = "邮箱验证码错误或已过期";
                return badRequestMap(errorMsg);
            }

            // 创建用户对象
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(request.getPassword());
            user.setEmail(request.getEmail());
            user.setPhone(request.getPhone());
            user.setRealName(request.getRealName());

            boolean registerSuccess = userService.register(user);
            if (registerSuccess) {
                // 注册成功后，获取新创建的用户信息
                User newUser = userService.getByUsername(user.getUsername());
                if (newUser != null) {
                    // 生成JWT Token，实现自动登录
                    String token = jwtUtil.generateToken(newUser.getId(), newUser.getUsername());

                    // 构建登录响应数据
                    LoginResponse loginResponse = new LoginResponse();
                    loginResponse.setToken(token);
                    loginResponse.setTokenType("Bearer");
                    loginResponse.setUserId(newUser.getId());
                    loginResponse.setUsername(newUser.getUsername());
                    loginResponse.setEmail(newUser.getEmail());
                    loginResponse.setRealName(newUser.getRealName());

                    result = successMap("注册成功，已自动登录", loginResponse);
                    success = true;
                } else {
                    // 理论上不应该发生这种情况
                    log.error("注册成功但无法获取用户信息: {}", user.getUsername());
                    result = internalServerErrorMap("注册成功但系统异常，请手动登录");
                    success = true; // 注册本身是成功的
                }
            } else {
                // 检查具体是用户名还是邮箱已存在
                if (userService.existsByUsername(user.getUsername())) {
                    errorMsg = "用户名已存在，请更换用户名";
                } else if (userService.existsByEmail(user.getEmail())) {
                    errorMsg = "邮箱已被注册，请更换邮箱或直接登录";
                } else {
                    errorMsg = "注册失败，请稍后重试";
                }
                result = badRequestMap(errorMsg);
            }
        } catch (Exception e) {
            log.error("用户注册异常", e);
            errorMsg = e.getMessage();
            result = internalServerErrorMap("系统异常");
        } finally {
            // 记录审计日志
            long executionTime = System.currentTimeMillis() - startTime;
            auditLogService.recordRegisterLog(request.getUsername(), request.getEmail(),
                                            httpRequest, success, errorMsg, executionTime);
        }

        return result;
    }

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public Map<String, Object> login(@Valid @RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        String errorMsg = null;
        boolean success = false;

        try {
            // 进行身份验证
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
                )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 获取用户详情
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();

            // 生成JWT Token
            String token = jwtUtil.generateToken(userDetails.getId(), userDetails.getUsername());

            // 构建响应
            LoginResponse loginResponse = new LoginResponse();
            loginResponse.setToken(token);
            loginResponse.setTokenType("Bearer");
            loginResponse.setUserId(userDetails.getId());
            loginResponse.setUsername(userDetails.getUsername());
            loginResponse.setEmail(userDetails.getEmail());
            loginResponse.setRealName(userDetails.getRealName());

            result.put("code", 200);
            result.put("message", "登录成功");
            result.put("data", loginResponse);

            success = true;

        } catch (Exception e) {
            log.error("用户登录失败", e);
            errorMsg = e.getMessage();

            // 根据异常类型返回不同的错误信息
            if (e.getMessage() != null) {
                if (e.getMessage().contains("Bad credentials")) {
                    result.put("code", 400);
                    result.put("message", "用户名/邮箱或密码错误");
                } else if (e.getMessage().contains("User account is disabled")) {
                    result.put("code", 400);
                    result.put("message", "账号已被禁用，请联系管理员");
                } else if (e.getMessage().contains("User account is locked")) {
                    result.put("code", 400);
                    result.put("message", "账号已被锁定，请联系管理员");
                } else if (e.getMessage().contains("用户不存在") || e.getMessage().contains("用户名不存在") || e.getMessage().contains("邮箱对应的用户不存在")) {
                    result.put("code", 400);
                    result.put("message", "用户名/邮箱或密码错误");
                } else {
                    result.put("code", 500);
                    result.put("message", "系统异常，请稍后重试");
                }
            } else {
                result.put("code", 400);
                result.put("message", "用户名/邮箱或密码错误");
            }
            result.put("data", null);
        } finally {
            // 记录审计日志
            long executionTime = System.currentTimeMillis() - startTime;
            auditLogService.recordLoginLog(loginRequest.getUsername(), request, success, errorMsg, executionTime);
        }

        return result;
    }

    @ApiOperation("获取当前用户信息")
    @GetMapping("/me")
    public Map<String, Object> getCurrentUser() {
        Map<String, Object> result = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
                CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
                
                // 获取最新的用户信息
                User user = userService.getById(userDetails.getId());
                if (user != null) {
                    user.setPassword(null); // 不返回密码
                    result.put("code", 200);
                    result.put("message", "获取成功");
                    result.put("data", user);
                } else {
                    result.put("code", 404);
                    result.put("message", "用户不存在");
                    result.put("data", null);
                }
            } else {
                result.put("code", 401);
                result.put("message", "未登录");
                result.put("data", null);
            }
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            result.put("code", 500);
            result.put("message", "系统异常");
            result.put("data", null);
        }
        return result;
    }

    @ApiOperation("用户登出")
    @PostMapping("/logout")
    public Map<String, Object> logout() {
        Map<String, Object> result = new HashMap<>();
        try {
            SecurityContextHolder.clearContext();
            result.put("code", 200);
            result.put("message", "登出成功");
            result.put("data", null);
        } catch (Exception e) {
            log.error("用户登出失败", e);
            result.put("code", 500);
            result.put("message", "系统异常");
            result.put("data", null);
        }
        return result;
    }
}
