package cloud.ipanda.jobplusv8.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 基础控制器类
 * 
 * 功能说明：
 * 1. 提供统一的响应格式处理方法
 * 2. 封装常用的成功和错误响应
 * 3. 减少控制器中的重复代码
 * 4. 统一API响应格式标准
 * 
 * 响应格式：
 * {
 *   "code": 200,
 *   "message": "操作成功",
 *   "data": {...}
 * }
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
public abstract class BaseController {

    /**
     * 创建成功响应（默认消息）
     * 
     * @param data 响应数据
     * @return 成功响应
     */
    protected ResponseEntity<Map<String, Object>> success(Object data) {
        return success("操作成功", data);
    }

    /**
     * 创建成功响应（自定义消息）
     * 
     * @param message 响应消息
     * @param data 响应数据
     * @return 成功响应
     */
    protected ResponseEntity<Map<String, Object>> success(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", message);
        response.put("data", data);
        return ResponseEntity.ok(response);
    }

    /**
     * 创建成功响应（仅消息，无数据）
     * 
     * @param message 响应消息
     * @return 成功响应
     */
    protected ResponseEntity<Map<String, Object>> success(String message) {
        return success(message, null);
    }

    /**
     * 创建错误响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @return 错误响应
     */
    protected ResponseEntity<Map<String, Object>> error(int code, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", code);
        response.put("message", message);
        response.put("data", null);
        
        // 根据错误码设置HTTP状态码
        int httpStatus = getHttpStatusFromCode(code);
        return ResponseEntity.status(httpStatus).body(response);
    }

    /**
     * 创建错误响应（默认400错误）
     * 
     * @param message 错误消息
     * @return 错误响应
     */
    protected ResponseEntity<Map<String, Object>> badRequest(String message) {
        return error(400, message);
    }

    /**
     * 创建未授权响应
     * 
     * @param message 错误消息
     * @return 未授权响应
     */
    protected ResponseEntity<Map<String, Object>> unauthorized(String message) {
        return error(401, message);
    }

    /**
     * 创建禁止访问响应
     * 
     * @param message 错误消息
     * @return 禁止访问响应
     */
    protected ResponseEntity<Map<String, Object>> forbidden(String message) {
        return error(403, message);
    }

    /**
     * 创建资源不存在响应
     * 
     * @param message 错误消息
     * @return 资源不存在响应
     */
    protected ResponseEntity<Map<String, Object>> notFound(String message) {
        return error(404, message);
    }

    /**
     * 创建服务器内部错误响应
     * 
     * @param message 错误消息
     * @return 服务器错误响应
     */
    protected ResponseEntity<Map<String, Object>> internalServerError(String message) {
        return error(500, message);
    }

    /**
     * 创建参数验证失败响应
     * 
     * @param message 验证失败消息
     * @return 参数验证失败响应
     */
    protected ResponseEntity<Map<String, Object>> validationError(String message) {
        return error(422, message);
    }

    /**
     * 创建请求过于频繁响应
     * 
     * @param message 限流消息
     * @return 请求过于频繁响应
     */
    protected ResponseEntity<Map<String, Object>> tooManyRequests(String message) {
        return error(429, message);
    }

    /**
     * 根据业务错误码映射HTTP状态码
     * 
     * @param code 业务错误码
     * @return HTTP状态码
     */
    private int getHttpStatusFromCode(int code) {
        if (code >= 200 && code < 300) {
            return 200; // 成功
        } else if (code >= 400 && code < 500) {
            return code; // 客户端错误，直接使用业务码
        } else if (code >= 500) {
            return 500; // 服务器错误
        } else {
            return 400; // 默认客户端错误
        }
    }

    /**
     * 处理异常并返回统一错误响应
     * 
     * @param e 异常对象
     * @param defaultMessage 默认错误消息
     * @return 错误响应
     */
    protected ResponseEntity<Map<String, Object>> handleException(Exception e, String defaultMessage) {
        log.error("控制器异常: {}", defaultMessage, e);
        
        // 可以根据异常类型返回不同的错误码
        if (e instanceof IllegalArgumentException) {
            return badRequest(e.getMessage());
        } else if (e instanceof SecurityException) {
            return forbidden(e.getMessage());
        } else {
            return internalServerError(defaultMessage);
        }
    }

    /**
     * 创建分页响应
     *
     * @param data 分页数据
     * @param total 总记录数
     * @param current 当前页码
     * @param size 每页大小
     * @return 分页响应
     */
    protected ResponseEntity<Map<String, Object>> pageSuccess(Object data, long total, int current, int size) {
        Map<String, Object> pageInfo = new HashMap<>();
        pageInfo.put("records", data);
        pageInfo.put("total", total);
        pageInfo.put("current", current);
        pageInfo.put("size", size);
        pageInfo.put("pages", (total + size - 1) / size); // 计算总页数

        return success("查询成功", pageInfo);
    }

    // ==================== 以下为Map返回类型的方法（适用于不使用ResponseEntity的控制器） ====================

    /**
     * 创建成功响应Map（默认消息）
     *
     * @param data 响应数据
     * @return 成功响应Map
     */
    protected Map<String, Object> successMap(Object data) {
        return successMap("操作成功", data);
    }

    /**
     * 创建成功响应Map（自定义消息）
     *
     * @param message 响应消息
     * @param data 响应数据
     * @return 成功响应Map
     */
    protected Map<String, Object> successMap(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    /**
     * 创建成功响应Map（仅消息，无数据）
     *
     * @param message 响应消息
     * @return 成功响应Map
     */
    protected Map<String, Object> successMap(String message) {
        return successMap(message, null);
    }

    /**
     * 创建错误响应Map
     *
     * @param code 错误码
     * @param message 错误消息
     * @return 错误响应Map
     */
    protected Map<String, Object> errorMap(int code, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", code);
        response.put("message", message);
        response.put("data", null);
        return response;
    }

    /**
     * 创建错误响应Map（默认400错误）
     *
     * @param message 错误消息
     * @return 错误响应Map
     */
    protected Map<String, Object> badRequestMap(String message) {
        return errorMap(400, message);
    }

    /**
     * 创建服务器内部错误响应Map
     *
     * @param message 错误消息
     * @return 服务器错误响应Map
     */
    protected Map<String, Object> internalServerErrorMap(String message) {
        return errorMap(500, message);
    }
}
