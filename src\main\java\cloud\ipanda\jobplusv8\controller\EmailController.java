package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.dto.SendCodeRequest;
import cloud.ipanda.jobplusv8.dto.VerifyCodeRequest;
import cloud.ipanda.jobplusv8.mail.TencentMailService;
import cloud.ipanda.jobplusv8.service.AuditLogService;
import cloud.ipanda.jobplusv8.service.EmailVerificationService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件服务控制器
 *
 * 功能说明：
 * 1. 提供邮件发送相关的REST API接口
 * 2. 支持验证码发送和验证功能（集成Redis）
 * 3. 提供面试邀请邮件发送
 * 4. 集成Swagger文档注解
 *
 * API分组：
 * - 验证码管理（使用Redis缓存）
 * - 邮件发送服务
 * - 面试邀请通知
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Api(tags = "邮件服务", description = "邮件发送、验证码管理和面试邀请相关API")
@RestController
@RequestMapping("/api/email")
public class EmailController extends BaseController {

    @Autowired
    private EmailVerificationService emailVerificationService;

    @Autowired
    private TencentMailService tencentMailService;

    @Autowired
    private AuditLogService auditLogService;

    /**
     * 发送验证码（使用Redis缓存）
     */
    @ApiOperation(value = "发送验证码", notes = "向指定邮箱发送验证码，支持注册、登录、重置密码等用途，使用Redis缓存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "验证码发送成功"),
            @ApiResponse(code = 429, message = "发送过于频繁"),
            @ApiResponse(code = 500, message = "发送失败")
    })
    @PostMapping("/send-code")
    public Map<String, Object> sendVerificationCode(@Valid @RequestBody SendCodeRequest request, HttpServletRequest httpRequest) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        String errorMsg = null;
        boolean success = false;

        try {
            log.info("【发送验证码API】邮箱: {}, 类型: {}", request.getEmail(), request.getType());

            // 检查发送间隔
            if (!emailVerificationService.canSendCode(request.getEmail(), request.getType())) {
                long remainingTime = emailVerificationService.getRemainingWaitTime(request.getEmail(), request.getType());
                errorMsg = "发送过于频繁，请等待 " + remainingTime + " 秒后再试";
                Map<String, Object> data = new HashMap<>();
                data.put("remainingTime", remainingTime);
                result = errorMap(429, errorMsg);
                result.put("data", data); // 覆盖data字段以包含remainingTime
                return result;
            }

            // 发送验证码
            boolean sendSuccess = emailVerificationService.sendVerificationCode(request.getEmail(), request.getType());
            if (sendSuccess) {
                result = successMap("验证码发送成功", null);
                success = true;
            } else {
                errorMsg = "验证码发送失败";
                result = internalServerErrorMap(errorMsg);
            }

        } catch (Exception e) {
            log.error("【发送验证码API失败】邮箱: {}, 错误: {}", request.getEmail(), e.getMessage(), e);
            errorMsg = e.getMessage();
            result = internalServerErrorMap("系统异常");
        } finally {
            // 记录审计日志
            long executionTime = System.currentTimeMillis() - startTime;
            auditLogService.recordEmailCodeLog(request.getEmail(), request.getType(),
                                              httpRequest, success, errorMsg, executionTime);
        }

        return result;
    }

    /**
     * 验证验证码（使用Redis缓存）
     */
    @ApiOperation(value = "验证验证码", notes = "验证用户输入的验证码是否正确，检查有效性和用途匹配")
    @ApiResponses({
            @ApiResponse(code = 200, message = "验证成功"),
            @ApiResponse(code = 400, message = "验证失败"),
            @ApiResponse(code = 500, message = "服务器错误")
    })
    @PostMapping("/verify-code")
    public Map<String, Object> verifyCode(@Valid @RequestBody VerifyCodeRequest request, HttpServletRequest httpRequest) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        String errorMsg = null;
        boolean success = false;

        try {
            log.info("【验证验证码API】邮箱: {}, 类型: {}", request.getEmail(), request.getType());

            boolean isValid = emailVerificationService.verifyCode(
                request.getEmail(),
                request.getCode(),
                request.getType()
            );

            if (isValid) {
                result = successMap("验证码验证成功", true);
                success = true;
            } else {
                errorMsg = "验证码错误或已过期";
                result = badRequestMap(errorMsg);
                result.put("data", false); // 覆盖data字段以返回false
            }

        } catch (Exception e) {
            log.error("【验证验证码API失败】邮箱: {}, 错误: {}", request.getEmail(), e.getMessage(), e);
            errorMsg = e.getMessage();
            result = internalServerErrorMap("系统异常");
        } finally {
            // 记录审计日志
            long executionTime = System.currentTimeMillis() - startTime;
            auditLogService.recordEmailVerifyLog(request.getEmail(), request.getType(),
                                                httpRequest, success, errorMsg, executionTime);
        }

        return result;
    }

    /**
     * 检查发送状态
     */
    @ApiOperation(value = "检查发送状态", notes = "检查指定邮箱是否可以发送验证码，获取剩余等待时间")
    @GetMapping("/send-status")
    public Map<String, Object> getSendStatus(
            @ApiParam(value = "邮箱地址", required = true) @RequestParam String email,
            @ApiParam(value = "验证码类型", required = true) @RequestParam String type) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean canSend = emailVerificationService.canSendCode(email, type);
            long remainingTime = canSend ? 0 : emailVerificationService.getRemainingWaitTime(email, type);

            Map<String, Object> data = new HashMap<>();
            data.put("canSend", canSend);
            data.put("remainingTime", remainingTime);

            result = successMap("获取成功", data);

        } catch (Exception e) {
            log.error("获取发送状态异常", e);
            result = internalServerErrorMap("系统异常");
        }
        return result;
    }

    /**
     * 发送面试邀请邮件
     */
    @ApiOperation(value = "发送面试邀请", notes = "向候选人发送面试邀请邮件，包含面试时间、地点等详细信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "邀请发送成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "发送失败")
    })
    @PostMapping("/send-interview-invitation")
    public Map<String, Object> sendInterviewInvitation(
            @ApiParam(value = "候选人邮箱", required = true, example = "<EMAIL>")
            @RequestParam String candidateEmail,

            @ApiParam(value = "候选人姓名", required = true, example = "张三")
            @RequestParam String candidateName,

            @ApiParam(value = "面试职位", required = true, example = "Java开发工程师")
            @RequestParam String position,

            @ApiParam(value = "面试时间", required = true, example = "2025年7月25日 14:00")
            @RequestParam String interviewTime,

            @ApiParam(value = "面试地点", required = true, example = "北京市朝阳区xxx大厦10楼")
            @RequestParam String interviewLocation,

            @ApiParam(value = "联系方式", required = true, example = "HR小王 电话：138xxxx1234")
            @RequestParam String contactInfo) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("【发送面试邀请API】候选人: {}, 邮箱: {}, 职位: {}", candidateName, candidateEmail, position);

            // 发送面试邀请邮件
            tencentMailService.sendInterviewInvitation(candidateEmail, candidateName, position,
                                          interviewTime, interviewLocation, contactInfo);

            Map<String, Object> data = new HashMap<>();
            data.put("candidateEmail", candidateEmail);
            data.put("candidateName", candidateName);
            data.put("position", position);
            data.put("interviewTime", interviewTime);
            data.put("sendTime", LocalDateTime.now());

            result = successMap("面试邀请发送成功", data);

        } catch (Exception e) {
            log.error("【发送面试邀请API失败】候选人: {}, 错误: {}", candidateName, e.getMessage(), e);
            result = internalServerErrorMap("面试邀请发送失败: " + e.getMessage());
        }
        return result;
    }
}
