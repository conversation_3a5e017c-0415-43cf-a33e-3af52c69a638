package cloud.ipanda.jobplusv8.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 示例控制器 - 展示如何使用BaseController
 * 
 * 功能说明：
 * 1. 演示继承BaseController的用法
 * 2. 展示统一响应格式的使用方法
 * 3. 提供两种返回类型的示例（ResponseEntity和Map）
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Api(tags = "示例接口")
@RestController
@RequestMapping("/api/example")
@Slf4j
public class ExampleController extends BaseController {

    /**
     * 示例：使用ResponseEntity返回类型
     */
    @ApiOperation("ResponseEntity示例")
    @GetMapping("/response-entity")
    public ResponseEntity<Map<String, Object>> responseEntityExample() {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("message", "这是使用ResponseEntity的示例");
            data.put("timestamp", System.currentTimeMillis());
            
            // 使用BaseController的success方法
            return success("操作成功", data);
            
        } catch (Exception e) {
            log.error("ResponseEntity示例异常", e);
            // 使用BaseController的错误处理方法
            return handleException(e, "ResponseEntity示例失败");
        }
    }

    /**
     * 示例：使用Map返回类型
     */
    @ApiOperation("Map返回类型示例")
    @GetMapping("/map")
    public Map<String, Object> mapExample() {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("message", "这是使用Map返回类型的示例");
            data.put("timestamp", System.currentTimeMillis());
            
            // 使用BaseController的successMap方法
            return successMap("操作成功", data);
            
        } catch (Exception e) {
            log.error("Map示例异常", e);
            // 使用BaseController的错误Map方法
            return internalServerErrorMap("Map示例失败");
        }
    }

    /**
     * 示例：各种错误响应
     */
    @ApiOperation("错误响应示例")
    @GetMapping("/error/{type}")
    public ResponseEntity<Map<String, Object>> errorExample(@PathVariable String type) {
        switch (type) {
            case "400":
                return badRequest("这是400错误示例");
            case "401":
                return unauthorized("这是401未授权示例");
            case "403":
                return forbidden("这是403禁止访问示例");
            case "404":
                return notFound("这是404资源不存在示例");
            case "422":
                return validationError("这是422参数验证失败示例");
            case "429":
                return tooManyRequests("这是429请求过于频繁示例");
            case "500":
                return internalServerError("这是500服务器错误示例");
            default:
                return badRequest("未知的错误类型");
        }
    }

    /**
     * 示例：分页响应
     */
    @ApiOperation("分页响应示例")
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> pageExample(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        
        try {
            // 模拟分页数据
            Map<String, Object> record1 = new HashMap<>();
            record1.put("id", 1);
            record1.put("name", "示例数据1");
            
            Map<String, Object> record2 = new HashMap<>();
            record2.put("id", 2);
            record2.put("name", "示例数据2");
            
            Object[] records = {record1, record2};
            long total = 100; // 模拟总记录数
            
            // 使用BaseController的分页方法
            return pageSuccess(records, total, current, size);
            
        } catch (Exception e) {
            log.error("分页示例异常", e);
            return handleException(e, "分页查询失败");
        }
    }

    /**
     * 示例：参数验证
     */
    @ApiOperation("参数验证示例")
    @PostMapping("/validate")
    public Map<String, Object> validateExample(@RequestParam String name) {
        // 参数验证
        if (name == null || name.trim().isEmpty()) {
            return badRequestMap("姓名不能为空");
        }
        
        if (name.length() > 20) {
            return badRequestMap("姓名长度不能超过20个字符");
        }
        
        // 验证通过
        Map<String, Object> data = new HashMap<>();
        data.put("name", name);
        data.put("message", "参数验证通过");
        
        return successMap("验证成功", data);
    }
}
