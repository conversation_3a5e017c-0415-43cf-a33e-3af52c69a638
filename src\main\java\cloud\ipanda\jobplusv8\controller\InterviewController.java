package cloud.ipanda.jobplusv8.controller;

import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 面试管理控制器
 * 
 * 功能说明：
 * 1. 提供面试相关的REST API接口
 * 2. 管理面试会话的创建、查询和状态更新
 * 3. 集成Swagger文档注解，自动生成API文档
 * 4. 支持面试过程的基本操作和查询
 * 
 * API分组：
 * - 面试会话管理
 * - 面试状态查询
 * - 系统健康检查
 * 
 * 技术特点：
 * - RESTful API设计
 * - 统一的响应格式
 * - 详细的Swagger注解
 * - 完善的错误处理
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/api/interview")
@Api(tags = "面试管理", description = "面试会话管理和语音识别相关API")
@Slf4j
public class InterviewController extends BaseController {

    /**
     * 系统健康检查接口
     * 
     * 功能说明：
     * 1. 检查系统运行状态
     * 2. 返回系统基本信息
     * 3. 用于监控和负载均衡健康检查
     * 
     * @return 系统状态信息
     */
    @GetMapping("/health")
    @ApiOperation(
            value = "系统健康检查", 
            notes = "检查面试语音识别系统的运行状态，返回系统基本信息和当前时间"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "系统运行正常"),
            @ApiResponse(code = 500, message = "系统异常")
    })
    public ResponseEntity<Map<String, Object>> health() {
        log.info("【健康检查】系统状态检查请求");
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "JobPlusV8 面试语音识别系统");
        response.put("version", "v0.0.1-SNAPSHOT");
        response.put("timestamp", LocalDateTime.now());
        response.put("description", "系统运行正常，语音识别服务可用");

        return success("系统健康检查通过", response);
    }

    /**
     * 创建面试会话
     * 
     * 功能说明：
     * 1. 创建新的面试会话
     * 2. 生成唯一的会话ID
     * 3. 初始化会话基本信息
     * 
     * @param interviewerName 面试官姓名
     * @param candidateName 候选人姓名
     * @param position 面试职位
     * @return 面试会话信息
     */
    @PostMapping("/session/create")
    @ApiOperation(
            value = "创建面试会话", 
            notes = "创建新的面试会话，生成唯一会话ID，用于后续的语音识别和记录管理"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "会话创建成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> createSession(
            @ApiParam(value = "面试官姓名", required = true, example = "张三")
            @RequestParam String interviewerName,
            
            @ApiParam(value = "候选人姓名", required = true, example = "李四") 
            @RequestParam String candidateName,
            
            @ApiParam(value = "面试职位", required = true, example = "Java开发工程师")
            @RequestParam String position
    ) {
        log.info("【创建面试会话】面试官: {}, 候选人: {}, 职位: {}", 
                interviewerName, candidateName, position);
        
        // 生成会话ID（实际项目中应使用UUID或其他唯一标识生成策略）
        String sessionId = "SESSION_" + System.currentTimeMillis();
        
        Map<String, Object> response = new HashMap<>();
        response.put("sessionId", sessionId);
        response.put("interviewerName", interviewerName);
        response.put("candidateName", candidateName);
        response.put("position", position);
        response.put("status", "CREATED");
        response.put("createTime", LocalDateTime.now());
        response.put("websocketUrl", "ws://localhost:80/channel/echo");

        return success("面试会话创建成功，可以开始语音识别", response);
    }

    /**
     * 查询面试会话信息
     * 
     * 功能说明：
     * 1. 根据会话ID查询面试信息
     * 2. 返回会话的详细状态
     * 3. 包含语音识别统计信息
     * 
     * @param sessionId 面试会话ID
     * @return 面试会话详细信息
     */
    @GetMapping("/session/{sessionId}")
    @ApiOperation(
            value = "查询面试会话", 
            notes = "根据会话ID查询面试会话的详细信息，包括参与人员、状态、时间等"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 404, message = "会话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getSession(
            @ApiParam(value = "面试会话ID", required = true, example = "SESSION_1642781234567")
            @PathVariable String sessionId
    ) {
        log.info("【查询面试会话】会话ID: {}", sessionId);
        
        // 模拟查询会话信息（实际项目中应从数据库查询）
        Map<String, Object> response = new HashMap<>();
        response.put("sessionId", sessionId);
        response.put("interviewerName", "张三");
        response.put("candidateName", "李四");
        response.put("position", "Java开发工程师");
        response.put("status", "IN_PROGRESS");
        response.put("createTime", LocalDateTime.now().minusMinutes(30));
        response.put("duration", "30分钟");
        response.put("speechRecognitionCount", 156);
        response.put("totalWords", 2340);
        response.put("websocketConnected", true);

        return success("查询成功", response);
    }

    /**
     * 结束面试会话
     * 
     * 功能说明：
     * 1. 结束指定的面试会话
     * 2. 生成面试总结报告
     * 3. 清理相关资源
     * 
     * @param sessionId 面试会话ID
     * @return 面试结束确认信息
     */
    @PostMapping("/session/{sessionId}/end")
    @ApiOperation(
            value = "结束面试会话", 
            notes = "结束指定的面试会话，生成面试总结，清理相关资源"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "会话结束成功"),
            @ApiResponse(code = 404, message = "会话不存在"),
            @ApiResponse(code = 400, message = "会话状态不允许结束"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> endSession(
            @ApiParam(value = "面试会话ID", required = true, example = "SESSION_1642781234567")
            @PathVariable String sessionId
    ) {
        log.info("【结束面试会话】会话ID: {}", sessionId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("sessionId", sessionId);
        response.put("status", "COMPLETED");
        response.put("endTime", LocalDateTime.now());
        response.put("totalDuration", "45分钟");
        response.put("finalSpeechCount", 203);
        response.put("finalWordCount", 3120);
        response.put("reportGenerated", true);

        return success("面试会话已成功结束，报告已生成", response);
    }
}
