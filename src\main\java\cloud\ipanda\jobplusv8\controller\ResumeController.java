package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.dto.ResumeResponse;
import cloud.ipanda.jobplusv8.dto.ResumeUpdateRequest;
import cloud.ipanda.jobplusv8.dto.ResumeUploadRequest;
import cloud.ipanda.jobplusv8.service.ResumeService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 简历管理控制器
 * 
 * 功能说明：
 * 1. 提供简历管理相关的REST API接口
 * 2. 支持简历文件上传（PDF、Word、TXT）
 * 3. 集成Gemini AI进行简历结构化解析
 * 4. 支持简历的CRUD操作和文件下载
 * 5. 支持设置默认简历和拖拽上传
 * 
 * API分组：
 * - 简历上传和文件管理
 * - 简历信息查询和更新
 * - 默认简历设置
 * - 简历解析和下载
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Api(tags = "简历管理", description = "简历上传、管理、解析和下载相关API")
@RestController
@RequestMapping("/api/resume")
public class ResumeController extends BaseController {

    @Autowired
    private ResumeService resumeService;

    @Autowired
    private JwtUtil jwtUtil;

    @ApiOperation(value = "上传简历", notes = "支持PDF、Word、TXT格式，自动调用Gemini AI进行结构化解析")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resumeName", value = "简历名称（姓名+岗位）", required = true, example = "张三-Java开发工程师"),
        @ApiImplicitParam(name = "file", value = "简历文件", required = true, dataType = "file"),
        @ApiImplicitParam(name = "isDefault", value = "是否设为默认简历", example = "false")
    })
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadResume(
            @RequestParam("resumeName") String resumeName,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "isDefault", defaultValue = "false") Boolean isDefault,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            
            // 构建上传请求
            ResumeUploadRequest uploadRequest = new ResumeUploadRequest();
            uploadRequest.setResumeName(resumeName);
            uploadRequest.setFile(file);
            uploadRequest.setIsDefault(isDefault);
            
            ResumeResponse response = resumeService.uploadResume(uploadRequest, userId);
            
            log.info("【上传简历成功】用户ID: {}, 简历ID: {}, 文件: {}", 
                    userId, response.getId(), file.getOriginalFilename());
            
            return success("简历上传成功，正在进行AI解析", response);
            
        } catch (Exception e) {
            log.error("【上传简历失败】错误: {}", e.getMessage(), e);
            return handleException(e, "上传简历失败");
        }
    }

    @ApiOperation(value = "获取简历列表", notes = "分页获取当前用户的简历列表")
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getResumeList(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            Page<ResumeResponse> page = resumeService.getUserResumes(userId, current, size);
            
            return success("查询成功", page);
            
        } catch (Exception e) {
            log.error("【获取简历列表失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取简历列表失败");
        }
    }

    @ApiOperation(value = "获取所有简历", notes = "获取当前用户的所有简历（不分页）")
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllResumes(HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            List<ResumeResponse> resumes = resumeService.getAllUserResumes(userId);
            
            return success("查询成功", resumes);
            
        } catch (Exception e) {
            log.error("【获取所有简历失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取简历失败");
        }
    }

    @ApiOperation(value = "获取简历详情", notes = "根据ID获取简历的详细信息")
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getResumeById(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long id,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            ResumeResponse response = resumeService.getResumeById(id, userId);
            
            return success("查询成功", response);
            
        } catch (Exception e) {
            log.error("【获取简历详情失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "获取简历详情失败");
        }
    }

    @ApiOperation(value = "更新简历信息", notes = "更新简历名称和默认设置")
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateResume(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long id,
            @Valid @RequestBody ResumeUpdateRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            Long userId = getCurrentUserId(httpRequest);
            ResumeResponse response = resumeService.updateResume(id, request, userId);
            
            log.info("【更新简历成功】简历ID: {}, 用户ID: {}", id, userId);
            return success("更新成功", response);
            
        } catch (Exception e) {
            log.error("【更新简历失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "更新简历失败");
        }
    }

    @ApiOperation(value = "删除简历", notes = "删除简历文件和数据库记录")
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteResume(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long id,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            boolean deleted = resumeService.deleteResume(id, userId);
            
            if (deleted) {
                log.info("【删除简历成功】简历ID: {}, 用户ID: {}", id, userId);
                return success("删除成功", null);
            } else {
                return error(500, "删除失败");
            }
            
        } catch (Exception e) {
            log.error("【删除简历失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "删除简历失败");
        }
    }

    @ApiOperation(value = "设置默认简历", notes = "将指定简历设为默认简历")
    @PostMapping("/{id}/set-default")
    public ResponseEntity<Map<String, Object>> setDefaultResume(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long id,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            boolean success = resumeService.setDefaultResume(id, userId);
            
            if (success) {
                log.info("【设置默认简历成功】简历ID: {}, 用户ID: {}", id, userId);
                return success("设置成功", null);
            } else {
                return error(500, "设置失败");
            }
            
        } catch (Exception e) {
            log.error("【设置默认简历失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "设置默认简历失败");
        }
    }

    @ApiOperation(value = "获取默认简历", notes = "获取当前用户的默认简历")
    @GetMapping("/default")
    public ResponseEntity<Map<String, Object>> getDefaultResume(HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            ResumeResponse response = resumeService.getDefaultResume(userId);
            
            if (response != null) {
                return success("查询成功", response);
            } else {
                return success("暂无默认简历", null);
            }
            
        } catch (Exception e) {
            log.error("【获取默认简历失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取默认简历失败");
        }
    }

    @ApiOperation(value = "下载简历", notes = "下载简历文件")
    @GetMapping("/{id}/download")
    public void downloadResume(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long id,
            HttpServletRequest request,
            HttpServletResponse response) {
        
        try {
            Long userId = getCurrentUserId(request);
            resumeService.downloadResume(id, userId, response);
            
        } catch (Exception e) {
            log.error("【下载简历失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "下载失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("【发送错误响应失败】", ex);
            }
        }
    }

    @ApiOperation(value = "重新解析简历", notes = "重新调用Gemini AI解析简历")
    @PostMapping("/{id}/reparse")
    public ResponseEntity<Map<String, Object>> reparseResume(
            @ApiParam(value = "简历ID", required = true) @PathVariable Long id,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            boolean success = resumeService.reparseResume(id, userId);
            
            if (success) {
                log.info("【重新解析简历成功】简历ID: {}, 用户ID: {}", id, userId);
                return success("重新解析已开始，请稍后查看结果", null);
            } else {
                return error(500, "重新解析失败");
            }
            
        } catch (Exception e) {
            log.error("【重新解析简历失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            return handleException(e, "重新解析简历失败");
        }
    }

    /**
     * 从请求中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        return jwtUtil.getCurrentUserIdFromRequest(request);
    }
}
