package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.service.SpeechSessionService;
import cloud.ipanda.jobplusv8.service.UserService;
import cloud.ipanda.jobplusv8.websocket.TencentInterviewWebsocket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

/**
 * 语音识别积分管理控制器
 *
 * 功能说明：
 * - 语音识别现在基于积分系统
 * - 1积分 = 1秒语音识别时长
 * - 用户使用语音识别时消耗积分
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Api(tags = "语音识别积分管理")
@RestController
@RequestMapping("/api/speech/duration")
@Slf4j
public class SpeechDurationController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private SpeechSessionService speechSessionService;

    /**
     * 获取当前用户的积分信息（用于语音识别）
     */
    @ApiOperation("获取用户积分信息（1积分=1秒语音识别）")
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getUserDurationInfo(Principal principal) {
        try {
            String username = principal.getName();
            User user = userService.getByUsername(username);

            if (user == null) {
                return error(404, "用户不存在");
            }
            
            // 现在基于积分计算，1积分=1秒
            long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
            long remainingDuration = currentPoints; // 积分就是可用秒数

            Map<String, Object> durationInfo = new HashMap<>();
            durationInfo.put("userId", user.getId());
            durationInfo.put("username", user.getUsername());
            durationInfo.put("currentPoints", currentPoints);
            durationInfo.put("remainingDuration", remainingDuration);
            durationInfo.put("pointsPerSecond", 1); // 1秒消耗1积分

            log.info("【查询用户积分】用户: {}, 当前积分: {}, 可用时长: {}s",
                    username, currentPoints, remainingDuration);
            
            return success(durationInfo);
            
        } catch (Exception e) {
            log.error("【查询用户时长失败】", e);
            return error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否有足够的积分进行语音识别
     */
    @ApiOperation("检查积分是否充足（1积分=1秒）")
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkDuration(Principal principal) {
        try {
            String username = principal.getName();
            User user = userService.getByUsername(username);

            if (user == null) {
                return error(404, "用户不存在");
            }
            
            boolean hasEnough = speechSessionService.hasEnoughDuration(user.getId());
            long remainingDuration = speechSessionService.getRemainingDuration(user.getId());
            
            Map<String, Object> checkResult = new HashMap<>();
            checkResult.put("hasEnoughDuration", hasEnough);
            checkResult.put("remainingDuration", remainingDuration);
            checkResult.put("canStartSession", hasEnough && remainingDuration > 0);
            
            return success(checkResult);
            
        } catch (Exception e) {
            log.error("【检查时长失败】", e);
            return error(500, "检查失败: " + e.getMessage());
        }
    }

    /**
     * 管理员：为用户充值时长
     */
    @ApiOperation("充值用户时长")
    @PostMapping("/admin/recharge")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> rechargeUserDuration(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("充值时长（秒）") @RequestParam Long duration,
            @ApiParam("充值原因") @RequestParam(required = false) String reason) {
        
        try {
            if (duration <= 0) {
                return error(400, "充值时长必须大于0");
            }
            
            User user = userService.getById(userId);
            if (user == null) {
                return error(404, "用户不存在");
            }
            
            boolean success = speechSessionService.addUserDuration(userId, duration);
            if (success) {
                log.info("【管理员充值时长】用户ID: {}, 充值时长: {}s, 原因: {}", userId, duration, reason);
                return success("充值成功，已为用户 " + user.getUsername() + " 充值 " + duration + " 秒", null);
            } else {
                return error(500, "充值失败");
            }
            
        } catch (Exception e) {
            log.error("【充值时长失败】", e);
            return error(500, "充值失败: " + e.getMessage());
        }
    }

    /**
     * 管理员：查询指定用户的时长信息
     */
    @ApiOperation("查询用户时长")
    @GetMapping("/admin/user/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getUserDurationByAdmin(
            @ApiParam("用户ID") @PathVariable Long userId) {
        
        try {
            User user = userService.getById(userId);
            if (user == null) {
                return error(404, "用户不存在");
            }
            
            // 现在基于积分计算，1积分=1秒
            long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
            long remainingDuration = currentPoints; // 积分就是可用秒数

            Map<String, Object> durationInfo = new HashMap<>();
            durationInfo.put("userId", user.getId());
            durationInfo.put("username", user.getUsername());
            durationInfo.put("email", user.getEmail());
            durationInfo.put("currentPoints", currentPoints);
            durationInfo.put("remainingDuration", remainingDuration);
            durationInfo.put("pointsPerSecond", 1); // 1秒消耗1积分
            
            return success(durationInfo);
            
        } catch (Exception e) {
            log.error("【管理员查询用户时长失败】", e);
            return error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 管理员：强制结束用户的所有活跃会话
     */
    @ApiOperation("强制结束用户会话")
    @PostMapping("/admin/force-end/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> forceEndUserSessions(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("结束原因") @RequestParam(required = false) String reason) {
        
        try {
            User user = userService.getById(userId);
            if (user == null) {
                return error(404, "用户不存在");
            }
            
            String endReason = reason != null ? reason : "管理员强制结束";
            
            // 结束数据库中的活跃会话
            int dbSessionCount = speechSessionService.forceEndUserSessions(userId, endReason);
            
            // 强制关闭WebSocket连接
            TencentInterviewWebsocket.forceCloseUserSessions(userId, endReason);
            
            log.info("【管理员强制结束会话】用户: {}, 结束会话数: {}, 原因: {}", 
                    user.getUsername(), dbSessionCount, endReason);
            
            return success("已强制结束用户 " + user.getUsername() + " 的 " + dbSessionCount + " 个会话", null);
            
        } catch (Exception e) {
            log.error("【强制结束会话失败】", e);
            return error(500, "操作失败: " + e.getMessage());
        }
    }

    /**
     * 管理员：获取系统语音识别统计信息
     */
    @ApiOperation("获取系统统计")
    @GetMapping("/admin/system-stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getSystemStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 当前活跃会话数
            stats.put("activeSessionCount", TencentInterviewWebsocket.getActiveSessionCount());
            
            // 处理超时会话
            int timeoutSessionCount = speechSessionService.handleTimeoutSessions();
            stats.put("timeoutSessionsHandled", timeoutSessionCount);
            
            return success(stats);
            
        } catch (Exception e) {
            log.error("【获取系统统计失败】", e);
            return error(500, "查询失败: " + e.getMessage());
        }
    }
}
