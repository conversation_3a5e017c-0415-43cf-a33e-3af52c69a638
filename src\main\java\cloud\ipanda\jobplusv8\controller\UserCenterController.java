package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.dto.*;
import cloud.ipanda.jobplusv8.service.UserCenterService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * 用户中心控制器
 * 
 * 功能说明：
 * 1. 提供用户中心相关的REST API接口
 * 2. 支持查看账号信息、修改密码
 * 3. 支持查看积分余额和消费记录
 * 4. 支持充值码充值功能
 * 5. 支持查看充值记录
 * 
 * API分组：
 * - 用户信息管理
 * - 积分查询和充值
 * - 记录查询
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Api(tags = "用户中心", description = "用户中心相关API，包括账号信息、积分管理、充值记录等")
@RestController
@RequestMapping("/api/user-center")
public class UserCenterController extends BaseController {

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private JwtUtil jwtUtil;

    @ApiOperation(value = "获取用户中心信息", notes = "获取当前用户的账号信息、积分余额等")
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getUserCenterInfo(HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            UserCenterResponse response = userCenterService.getUserCenterInfo(userId);
            
            log.info("【获取用户中心信息成功】用户ID: {}", userId);
            return success("查询成功", response);
            
        } catch (Exception e) {
            log.error("【获取用户中心信息失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取用户信息失败");
        }
    }

    @ApiOperation(value = "修改密码", notes = "修改当前用户的登录密码")
    @PostMapping("/change-password")
    public ResponseEntity<Map<String, Object>> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            Long userId = getCurrentUserId(httpRequest);
            boolean success = userCenterService.changePassword(userId, request);
            
            if (success) {
                log.info("【修改密码成功】用户ID: {}", userId);
                return success("密码修改成功", null);
            } else {
                return error(500, "密码修改失败");
            }
            
        } catch (Exception e) {
            log.error("【修改密码失败】错误: {}", e.getMessage(), e);
            return badRequest(e.getMessage());
        }
    }

    @ApiOperation(value = "获取充值记录", notes = "分页获取当前用户的积分充值记录")
    @GetMapping("/recharge-records")
    public ResponseEntity<Map<String, Object>> getRechargeRecords(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            Page<PointsRechargeResponse> page = userCenterService.getRechargeRecords(userId, current, size);
            
            return success("查询成功", page);
            
        } catch (Exception e) {
            log.error("【获取充值记录失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取充值记录失败");
        }
    }

    @ApiOperation(value = "获取消费记录", notes = "分页获取当前用户的积分消费记录")
    @GetMapping("/consumption-records")
    public ResponseEntity<Map<String, Object>> getConsumptionRecords(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {
        
        try {
            Long userId = getCurrentUserId(request);
            Page<PointsConsumptionResponse> page = userCenterService.getConsumptionRecords(userId, current, size);
            
            return success("查询成功", page);
            
        } catch (Exception e) {
            log.error("【获取消费记录失败】错误: {}", e.getMessage(), e);
            return handleException(e, "获取消费记录失败");
        }
    }

    @ApiOperation(value = "充值码充值", notes = "使用充值码为当前用户充值积分")
    @PostMapping("/recharge")
    public ResponseEntity<Map<String, Object>> rechargeByCode(
            @Valid @RequestBody RechargeRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            Long userId = getCurrentUserId(httpRequest);
            PointsRechargeResponse response = userCenterService.rechargeByCode(userId, request);
            
            log.info("【充值码充值成功】用户ID: {}, 充值积分: {}", userId, response.getPointsAmount());
            return success("充值成功", response);
            
        } catch (Exception e) {
            log.error("【充值码充值失败】错误: {}", e.getMessage(), e);
            return badRequest(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员直充", notes = "管理员为指定用户直接充值积分")
    @PostMapping("/admin-recharge")
    public ResponseEntity<Map<String, Object>> adminRecharge(
            @Valid @RequestBody AdminRechargeRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            Long operatorId = getCurrentUserId(httpRequest);
            String operatorUsername = getCurrentUsername(httpRequest);
            
            PointsRechargeResponse response = userCenterService.adminRecharge(operatorId, operatorUsername, request);
            
            log.info("【管理员直充成功】操作员: {}, 目标用户: {}, 充值积分: {}", 
                    operatorUsername, request.getUserId(), request.getPointsAmount());
            return success("充值成功", response);
            
        } catch (Exception e) {
            log.error("【管理员直充失败】错误: {}", e.getMessage(), e);
            return badRequest(e.getMessage());
        }
    }

    /**
     * 从请求中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        return jwtUtil.getCurrentUserIdFromRequest(request);
    }

    /**
     * 从请求中获取当前用户名
     */
    private String getCurrentUsername(HttpServletRequest request) {
        try {
            String token = jwtUtil.getTokenFromRequest(request);
            if (token != null) {
                return jwtUtil.getUsernameFromToken(token);
            }
        } catch (Exception e) {
            log.error("从请求中获取用户名失败", e);
        }
        return null;
    }
}
