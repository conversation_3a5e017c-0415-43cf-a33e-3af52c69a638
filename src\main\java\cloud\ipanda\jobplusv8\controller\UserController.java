package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.service.UserService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @ApiOperation("获取用户信息")
    @PreAuthorize("hasAuthority('user:read')")
    @GetMapping("/{id}")
    public Map<String, Object> getUserById(@ApiParam("用户ID") @PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            User user = userService.getById(id);
            if (user != null) {
                // 清空密码字段
                user.setPassword(null);
                result.put("code", 200);
                result.put("message", "获取成功");
                result.put("data", user);
            } else {
                result.put("code", 404);
                result.put("message", "用户不存在");
                result.put("data", null);
            }
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            result.put("code", 500);
            result.put("message", "系统异常");
            result.put("data", null);
        }
        return result;
    }

    @ApiOperation("分页查询用户列表")
    @PreAuthorize("hasAuthority('user:read')")
    @GetMapping("/page")
    public Map<String, Object> getUserPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户名") @RequestParam(required = false) String username,
            @ApiParam("邮箱") @RequestParam(required = false) String email) {
        Map<String, Object> result = new HashMap<>();
        try {
            Page<User> page = new Page<>(current, size);
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            
            if (username != null && !username.trim().isEmpty()) {
                queryWrapper.like("username", username);
            }
            if (email != null && !email.trim().isEmpty()) {
                queryWrapper.like("email", email);
            }
            
            // 不查询密码字段
            queryWrapper.select("id", "username", "email", "phone", "real_name", "status", "create_time", "update_time");
            
            Page<User> userPage = userService.page(page, queryWrapper);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", userPage);
        } catch (Exception e) {
            log.error("分页查询用户列表异常", e);
            result.put("code", 500);
            result.put("message", "系统异常");
            result.put("data", null);
        }
        return result;
    }
}
