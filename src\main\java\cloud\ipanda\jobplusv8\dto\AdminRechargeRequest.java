package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 管理员充值请求DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "管理员充值请求")
public class AdminRechargeRequest {

    @ApiModelProperty(value = "目标用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "充值积分数量", required = true, example = "1000")
    @NotNull(message = "积分数量不能为空")
    @Min(value = 1, message = "积分数量必须大于0")
    private Long pointsAmount;

    @ApiModelProperty(value = "备注信息", example = "管理员手动充值")
    private String remark;
}
