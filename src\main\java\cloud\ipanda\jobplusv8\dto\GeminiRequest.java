package cloud.ipanda.jobplusv8.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Gemini API请求DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GeminiRequest {
    
    private List<Content> contents;
    private SystemInstruction systemInstruction;
    private List<SafetySetting> safetySettings;
    private List<Object> tools;
    private GenerationConfig generationConfig;
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {
        private List<Part> parts;
        private String role;
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Part {
        private String text;
        private InlineData inlineData;
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InlineData {
        private String data;
        private String mimeType;
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SystemInstruction {
        private List<Part> parts;
        private String role;
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SafetySetting {
        private String category;
        private String threshold;
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GenerationConfig {
        private double temperature;
        private String responseMimeType;
        private ThinkingConfig thinkingConfig;
    }
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ThinkingConfig {
        private boolean includeThoughts;
        private int thinkingBudget;
    }
}
