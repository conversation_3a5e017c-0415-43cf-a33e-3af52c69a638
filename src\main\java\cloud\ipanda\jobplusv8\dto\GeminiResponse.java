package cloud.ipanda.jobplusv8.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Gemini API响应DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GeminiResponse {
    
    private List<Candidate> candidates;
    private UsageMetadata usageMetadata;
    private String modelVersion;
    private String responseId;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Candidate {
        private Content content;
        private String finishReason;
        private int index;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {
        private List<Part> parts;
        private String role;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Part {
        private String text;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UsageMetadata {
        private int promptTokenCount;
        private int candidatesTokenCount;
        private int totalTokenCount;
        private List<PromptTokenDetail> promptTokensDetails;
        private int thoughtsTokenCount;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PromptTokenDetail {
        private String modality;
        private int tokenCount;
    }
}
