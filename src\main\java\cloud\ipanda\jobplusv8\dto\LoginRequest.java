package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@ApiModel("登录请求")
public class LoginRequest {

    @ApiModelProperty(value = "用户名或邮箱", required = true, example = "admin 或 <EMAIL>")
    @NotBlank(message = "用户名或邮箱不能为空")
    private String username;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "密码不能为空")
    private String password;
}
