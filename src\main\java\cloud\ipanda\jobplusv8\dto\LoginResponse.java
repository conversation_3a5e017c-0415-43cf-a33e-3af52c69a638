package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@ApiModel("登录响应")
public class LoginResponse {

    @ApiModelProperty("访问令牌")
    private String token;

    @ApiModelProperty("令牌类型")
    private String tokenType;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("真实姓名")
    private String realName;
}
