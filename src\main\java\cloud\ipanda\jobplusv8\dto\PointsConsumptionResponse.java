package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分消费记录响应DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "积分消费记录响应")
public class PointsConsumptionResponse {

    @ApiModelProperty(value = "消费记录ID")
    private Long id;

    @ApiModelProperty(value = "消费类型：1-面试功能，2-语音识别，3-AI答复，4-其他")
    private Integer consumptionType;

    @ApiModelProperty(value = "消费类型描述")
    private String consumptionTypeDesc;

    @ApiModelProperty(value = "消费积分数量")
    private Long pointsAmount;

    @ApiModelProperty(value = "消费前积分余额")
    private Long pointsBefore;

    @ApiModelProperty(value = "消费后积分余额")
    private Long pointsAfter;

    @ApiModelProperty(value = "关联业务ID")
    private String businessId;

    @ApiModelProperty(value = "业务描述")
    private String businessDesc;

    @ApiModelProperty(value = "消费状态：0-失败，1-成功，2-退款")
    private Integer status;

    @ApiModelProperty(value = "消费状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "消费时间")
    private LocalDateTime createTime;

    /**
     * 设置消费类型描述
     */
    public void setConsumptionTypeDesc() {
        if (consumptionType == null) {
            consumptionTypeDesc = "未知";
            return;
        }
        
        switch (consumptionType) {
            case 1:
                consumptionTypeDesc = "面试功能";
                break;
            case 2:
                consumptionTypeDesc = "语音识别";
                break;
            case 3:
                consumptionTypeDesc = "AI答复";
                break;
            case 4:
                consumptionTypeDesc = "其他";
                break;
            default:
                consumptionTypeDesc = "未知";
                break;
        }
    }

    /**
     * 设置状态描述
     */
    public void setStatusDesc() {
        if (status == null) {
            statusDesc = "未知";
            return;
        }
        
        switch (status) {
            case 0:
                statusDesc = "失败";
                break;
            case 1:
                statusDesc = "成功";
                break;
            case 2:
                statusDesc = "退款";
                break;
            default:
                statusDesc = "未知";
                break;
        }
    }
}
