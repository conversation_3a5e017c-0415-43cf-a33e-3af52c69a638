package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 积分充值记录响应DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "积分充值记录响应")
public class PointsRechargeResponse {

    @ApiModelProperty(value = "充值记录ID")
    private Long id;

    @ApiModelProperty(value = "充值类型：1-充值码充值，2-管理员直充")
    private Integer rechargeType;

    @ApiModelProperty(value = "充值类型描述")
    private String rechargeTypeDesc;

    @ApiModelProperty(value = "充值码（脱敏显示）")
    private String rechargeCodeMasked;

    @ApiModelProperty(value = "充值积分数量")
    private Long pointsAmount;

    @ApiModelProperty(value = "充值前积分余额")
    private Long pointsBefore;

    @ApiModelProperty(value = "充值后积分余额")
    private Long pointsAfter;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "操作员用户名")
    private String operatorUsername;

    @ApiModelProperty(value = "充值状态：0-失败，1-成功，2-处理中")
    private Integer status;

    @ApiModelProperty(value = "充值状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "充值时间")
    private LocalDateTime createTime;

    /**
     * 设置充值类型描述
     */
    public void setRechargeTypeDesc() {
        if (rechargeType == null) {
            rechargeTypeDesc = "未知";
            return;
        }
        
        switch (rechargeType) {
            case 1:
                rechargeTypeDesc = "充值码充值";
                break;
            case 2:
                rechargeTypeDesc = "管理员直充";
                break;
            default:
                rechargeTypeDesc = "未知";
                break;
        }
    }

    /**
     * 设置状态描述
     */
    public void setStatusDesc() {
        if (status == null) {
            statusDesc = "未知";
            return;
        }
        
        switch (status) {
            case 0:
                statusDesc = "失败";
                break;
            case 1:
                statusDesc = "成功";
                break;
            case 2:
                statusDesc = "处理中";
                break;
            default:
                statusDesc = "未知";
                break;
        }
    }

    /**
     * 设置充值码脱敏显示
     */
    public void setRechargeCodeMasked(String originalCode) {
        if (originalCode == null || originalCode.length() <= 8) {
            rechargeCodeMasked = originalCode;
            return;
        }
        
        // 显示前4位和后4位，中间用*代替
        String prefix = originalCode.substring(0, 4);
        String suffix = originalCode.substring(originalCode.length() - 4);
        int maskLength = originalCode.length() - 8;
        StringBuilder mask = new StringBuilder();
        for (int i = 0; i < maskLength; i++) {
            mask.append("*");
        }
        rechargeCodeMasked = prefix + mask.toString() + suffix;
    }
}
