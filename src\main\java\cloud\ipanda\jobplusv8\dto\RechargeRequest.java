package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 充值请求DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "充值请求")
public class RechargeRequest {

    @ApiModelProperty(value = "充值码", required = true, example = "RC2025080412345678")
    @NotBlank(message = "充值码不能为空")
    private String rechargeCode;
}
