package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@ApiModel("用户注册请求")
public class RegisterRequest {

    @ApiModelProperty(value = "用户名", required = true, example = "testuser")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度应在3-20位之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @ApiModelProperty(value = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度应在6-20位之间")
    private String password;

    @ApiModelProperty(value = "邮箱地址", required = true, example = "<EMAIL>")
    @NotBlank(message = "邮箱地址不能为空")
    @Email(message = "邮箱地址格式不正确")
    private String email;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    @Size(max = 20, message = "真实姓名长度不能超过20位")
    private String realName;

    @ApiModelProperty(value = "邮箱验证码", required = true, example = "123456")
    @NotBlank(message = "邮箱验证码不能为空")
    @Size(min = 4, max = 8, message = "验证码长度应在4-8位之间")
    private String emailCode;
}
