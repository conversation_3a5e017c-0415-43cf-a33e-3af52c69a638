package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 简历响应DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "简历信息响应")
public class ResumeResponse {

    @ApiModelProperty(value = "简历ID")
    private Long id;

    @ApiModelProperty(value = "简历名称")
    private String resumeName;

    @ApiModelProperty(value = "原始文件名")
    private String originalFilename;

    @ApiModelProperty(value = "文件大小（字节）")
    private Long fileSize;

    @ApiModelProperty(value = "文件大小（格式化）")
    private String fileSizeFormatted;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "是否为默认简历")
    private Boolean isDefault;

    @ApiModelProperty(value = "解析状态：0-未解析，1-解析中，2-解析成功，3-解析失败")
    private Integer parseStatus;

    @ApiModelProperty(value = "解析状态描述")
    private String parseStatusDesc;

    @ApiModelProperty(value = "解析结果")
    private Object parsedContent;

    @ApiModelProperty(value = "解析错误信息")
    private String parseError;

    @ApiModelProperty(value = "解析时间")
    private LocalDateTime parsedAt;

    @ApiModelProperty(value = "上传时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 格式化文件大小
     */
    public void formatFileSize() {
        if (fileSize == null) {
            fileSizeFormatted = "0 B";
            return;
        }
        
        if (fileSize < 1024) {
            fileSizeFormatted = fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            fileSizeFormatted = String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            fileSizeFormatted = String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            fileSizeFormatted = String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 设置解析状态描述
     */
    public void setParseStatusDesc() {
        if (parseStatus == null) {
            parseStatusDesc = "未知";
            return;
        }
        
        switch (parseStatus) {
            case 0:
                parseStatusDesc = "未解析";
                break;
            case 1:
                parseStatusDesc = "解析中";
                break;
            case 2:
                parseStatusDesc = "解析成功";
                break;
            case 3:
                parseStatusDesc = "解析失败";
                break;
            default:
                parseStatusDesc = "未知";
                break;
        }
    }
}
