package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 简历更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "简历更新请求")
public class ResumeUpdateRequest {

    @ApiModelProperty(value = "简历名称（姓名+岗位）", required = true, example = "张三-高级Java开发工程师")
    @NotBlank(message = "简历名称不能为空")
    private String resumeName;

    @ApiModelProperty(value = "是否设为默认简历", example = "true")
    private Boolean isDefault;
}
