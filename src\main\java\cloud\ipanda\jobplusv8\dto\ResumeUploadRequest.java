package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 简历上传请求DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "简历上传请求")
public class ResumeUploadRequest {

    @ApiModelProperty(value = "简历名称（姓名+岗位）", required = true, example = "张三-Java开发工程师")
    @NotBlank(message = "简历名称不能为空")
    private String resumeName;

    @ApiModelProperty(value = "简历文件", required = true)
    @NotNull(message = "简历文件不能为空")
    private MultipartFile file;

    @ApiModelProperty(value = "是否设为默认简历", example = "false")
    private Boolean isDefault = false;
}
