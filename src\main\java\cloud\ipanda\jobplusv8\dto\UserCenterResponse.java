package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户中心信息响应DTO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(description = "用户中心信息响应")
public class UserCenterResponse {

    @ApiModelProperty(value = "用户ID")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "用户状态：0-禁用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "用户状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "当前积分余额")
    private Long points;

    @ApiModelProperty(value = "注册时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "最后更新时间")
    private LocalDateTime updateTime;

    /**
     * 设置状态描述
     */
    public void setStatusDesc() {
        if (status == null) {
            statusDesc = "未知";
            return;
        }

        switch (status) {
            case 0:
                statusDesc = "禁用";
                break;
            case 1:
                statusDesc = "正常";
                break;
            default:
                statusDesc = "未知";
                break;
        }
    }
}
