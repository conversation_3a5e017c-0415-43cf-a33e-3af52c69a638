package cloud.ipanda.jobplusv8.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 验证验证码请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@ApiModel("验证验证码请求")
public class VerifyCodeRequest {

    @ApiModelProperty(value = "邮箱地址", required = true, example = "<EMAIL>")
    @NotBlank(message = "邮箱地址不能为空")
    @Email(message = "邮箱地址格式不正确")
    private String email;

    @ApiModelProperty(value = "验证码", required = true, example = "123456")
    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 8, message = "验证码长度应在4-8位之间")
    private String code;

    @ApiModelProperty(value = "验证码类型", required = true, example = "register", 
                     notes = "可选值：register-注册，login-登录，reset-重置密码")
    @NotBlank(message = "验证码类型不能为空")
    @Pattern(regexp = "^(register|login|reset)$", message = "验证码类型只能是：register、login、reset")
    private String type;
}
