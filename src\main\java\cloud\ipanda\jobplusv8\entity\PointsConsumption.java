package cloud.ipanda.jobplusv8.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分消费记录实体类
 * 
 * 功能说明：
 * 1. 记录用户积分消费的详细信息
 * 2. 支持多种消费类型（面试、语音识别、AI答复等）
 * 3. 记录消费前后的积分变化
 * 4. 提供完整的消费审计追踪
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_points_consumption")
public class PointsConsumption implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 消费类型：1-面试功能，2-语音识别，3-AI答复，4-其他
     */
    @TableField("consumption_type")
    private Integer consumptionType;

    /**
     * 消费积分数量
     */
    @TableField("points_amount")
    private Long pointsAmount;

    /**
     * 消费前积分余额
     */
    @TableField("points_before")
    private Long pointsBefore;

    /**
     * 消费后积分余额
     */
    @TableField("points_after")
    private Long pointsAfter;

    /**
     * 关联业务ID（如面试会话ID、语音识别任务ID等）
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 业务描述
     */
    @TableField("business_desc")
    private String businessDesc;

    /**
     * 消费状态：0-失败，1-成功，2-退款
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 消费类型常量
     */
    public static class ConsumptionType {
        public static final int INTERVIEW = 1;        // 面试功能
        public static final int SPEECH_RECOGNITION = 2; // 语音识别
        public static final int AI_REPLY = 3;         // AI答复
        public static final int OTHER = 4;            // 其他
    }

    /**
     * 消费状态常量
     */
    public static class Status {
        public static final int FAILED = 0;    // 失败
        public static final int SUCCESS = 1;   // 成功
        public static final int REFUNDED = 2;  // 退款
    }
}
