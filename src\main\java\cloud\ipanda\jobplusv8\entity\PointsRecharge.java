package cloud.ipanda.jobplusv8.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 积分充值记录实体类
 * 
 * 功能说明：
 * 1. 记录用户积分充值的详细信息
 * 2. 支持充值码充值和管理员直充两种方式
 * 3. 记录充值前后的积分变化
 * 4. 提供完整的充值审计追踪
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_points_recharge")
public class PointsRecharge implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 充值类型：1-充值码充值，2-管理员直充
     */
    @TableField("recharge_type")
    private Integer rechargeType;

    /**
     * 充值码（充值码充值时使用）
     */
    @TableField("recharge_code")
    private String rechargeCode;

    /**
     * 充值积分数量
     */
    @TableField("points_amount")
    private Long pointsAmount;

    /**
     * 充值前积分余额
     */
    @TableField("points_before")
    private Long pointsBefore;

    /**
     * 充值后积分余额
     */
    @TableField("points_after")
    private Long pointsAfter;

    /**
     * 充值金额（如果有的话）
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 操作员ID（管理员直充时记录）
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 操作员用户名
     */
    @TableField("operator_username")
    private String operatorUsername;

    /**
     * 充值状态：0-失败，1-成功，2-处理中
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 充值类型常量
     */
    public static class RechargeType {
        public static final int RECHARGE_CODE = 1;  // 充值码充值
        public static final int ADMIN_DIRECT = 2;   // 管理员直充
    }

    /**
     * 充值状态常量
     */
    public static class Status {
        public static final int FAILED = 0;      // 失败
        public static final int SUCCESS = 1;     // 成功
        public static final int PROCESSING = 2;  // 处理中
    }
}
