package cloud.ipanda.jobplusv8.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值码实体类
 * 
 * 功能说明：
 * 1. 管理系统中的充值码信息
 * 2. 支持充值码的生成、使用和管理
 * 3. 记录充值码的使用状态和历史
 * 4. 支持批量生成和管理
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("recharge_code")
public class RechargeCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 充值码（加密后的）
     */
    @TableField("code")
    private String code;

    /**
     * 充值码原始值（用于生成，不存储）
     */
    @TableField(exist = false)
    private String originalCode;

    /**
     * 积分数量
     */
    @TableField("points_amount")
    private Long pointsAmount;

    /**
     * 充值金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 使用状态：0-未使用，1-已使用，2-已过期
     */
    @TableField("status")
    private Integer status;

    /**
     * 使用用户ID
     */
    @TableField("used_user_id")
    private Long usedUserId;

    /**
     * 使用时间
     */
    @TableField("used_time")
    private LocalDateTime usedTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 生成批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 创建者ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建者用户名
     */
    @TableField("creator_username")
    private String creatorUsername;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 充值码状态常量
     */
    public static class Status {
        public static final int UNUSED = 0;    // 未使用
        public static final int USED = 1;      // 已使用
        public static final int EXPIRED = 2;   // 已过期
    }
}
