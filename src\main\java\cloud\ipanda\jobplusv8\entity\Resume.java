package cloud.ipanda.jobplusv8.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 简历实体类
 * 
 * 功能说明：
 * 1. 管理用户上传的简历文件信息
 * 2. 支持PDF、Word、TXT等多种格式
 * 3. 记录文件的基本信息和状态
 * 4. 支持设置默认简历
 * 5. 集成Gemini AI结构化解析结果
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_resume")
public class Resume implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 简历名称（姓名+岗位）
     */
    @TableField("resume_name")
    private String resumeName;

    /**
     * 原始文件名
     */
    @TableField("original_filename")
    private String originalFilename;

    /**
     * 存储文件名（系统生成的唯一文件名）
     */
    @TableField("stored_filename")
    private String storedFilename;

    /**
     * 文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件类型（pdf, docx, doc, txt）
     */
    @TableField("file_type")
    private String fileType;

    /**
     * MIME类型
     */
    @TableField("mime_type")
    private String mimeType;

    /**
     * 是否为默认简历：0-否，1-是
     */
    @TableField("is_default")
    private Integer isDefault;

    /**
     * 解析状态：0-未解析，1-解析中，2-解析成功，3-解析失败
     */
    @TableField("parse_status")
    private Integer parseStatus;

    /**
     * Gemini解析结果（JSON格式）
     */
    @TableField("parsed_content")
    private String parsedContent;

    /**
     * 解析错误信息
     */
    @TableField("parse_error")
    private String parseError;

    /**
     * 解析时间
     */
    @TableField("parsed_at")
    private LocalDateTime parsedAt;

    /**
     * 简历状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 解析状态常量
     */
    public static class ParseStatus {
        public static final int NOT_PARSED = 0;    // 未解析
        public static final int PARSING = 1;       // 解析中
        public static final int PARSED = 2;        // 解析成功
        public static final int PARSE_FAILED = 3;  // 解析失败
    }

    /**
     * 状态常量
     */
    public static class Status {
        public static final int DISABLED = 0;  // 禁用
        public static final int ENABLED = 1;   // 启用
    }

    /**
     * 默认简历常量
     */
    public static class DefaultFlag {
        public static final int NOT_DEFAULT = 0;  // 非默认
        public static final int IS_DEFAULT = 1;   // 默认简历
    }

    /**
     * 支持的文件类型
     */
    public static class FileType {
        public static final String PDF = "pdf";
        public static final String DOCX = "docx";
        public static final String DOC = "doc";
        public static final String TXT = "txt";
    }
}
