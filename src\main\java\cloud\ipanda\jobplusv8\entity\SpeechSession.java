package cloud.ipanda.jobplusv8.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 语音识别会话实体类
 * 
 * 功能说明：
 * 1. 记录用户的语音识别会话信息
 * 2. 管理会话的生命周期和状态
 * 3. 记录会话的时长和使用情况
 * 4. 支持录音文件的持久化存储
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("speech_sessions")
public class SpeechSession implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID（唯一标识）
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * WebSocket会话ID
     */
    @TableField("websocket_session_id")
    private String websocketSessionId;

    /**
     * 会话状态：1-活跃，2-已结束，3-超时结束，4-强制结束
     */
    @TableField("status")
    private Integer status;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 会话持续时长（秒）
     */
    @TableField("duration")
    private Long duration;

    /**
     * 录音文件路径
     */
    @TableField("audio_file_path")
    private String audioFilePath;

    /**
     * 录音文件大小（字节）
     */
    @TableField("audio_file_size")
    private Long audioFileSize;

    /**
     * 识别结果总数
     */
    @TableField("recognition_count")
    private Integer recognitionCount;

    /**
     * 识别总字数
     */
    @TableField("total_words")
    private Integer totalWords;

    /**
     * 客户端IP地址
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 结束原因：NORMAL-正常结束，TIMEOUT-超时，FORCE-强制结束，ERROR-错误结束
     */
    @TableField("end_reason")
    private String endReason;

    /**
     * 备注信息
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 会话状态枚举
     */
    public static class Status {
        public static final int ACTIVE = 1;      // 活跃
        public static final int ENDED = 2;       // 已结束
        public static final int TIMEOUT = 3;     // 超时结束
        public static final int FORCED = 4;      // 强制结束
    }

    /**
     * 结束原因枚举
     */
    public static class EndReason {
        public static final String NORMAL = "NORMAL";     // 正常结束
        public static final String TIMEOUT = "TIMEOUT";   // 超时结束
        public static final String FORCE = "FORCE";       // 强制结束
        public static final String ERROR = "ERROR";       // 错误结束
        public static final String NO_DURATION = "NO_DURATION"; // 时长不足
    }
}
