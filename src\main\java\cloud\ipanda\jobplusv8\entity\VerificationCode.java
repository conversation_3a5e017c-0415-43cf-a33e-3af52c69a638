package cloud.ipanda.jobplusv8.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 验证码实体类
 * 
 * 功能说明：
 * 1. 封装验证码相关信息
 * 2. 包含验证码内容、创建时间、过期时间等
 * 3. 支持验证码状态管理和验证
 * 4. 用于缓存存储和业务逻辑处理
 * 
 * 属性说明：
 * - code: 验证码内容（6位数字）
 * - email: 接收验证码的邮箱地址
 * - createTime: 验证码创建时间
 * - expireTime: 验证码过期时间
 * - used: 验证码是否已使用
 * - purpose: 验证码用途（注册、登录、重置密码等）
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@Data  // Lombok注解，自动生成getter、setter、toString、equals、hashCode方法
@NoArgsConstructor  // 无参构造函数
@AllArgsConstructor  // 全参构造函数
public class VerificationCode {
    
    /**
     * 验证码内容
     * 通常为6位数字字符串
     */
    private String code;
    
    /**
     * 接收验证码的邮箱地址
     */
    private String email;
    
    /**
     * 验证码创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 验证码过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 验证码是否已使用
     * true: 已使用，false: 未使用
     */
    private Boolean used;
    
    /**
     * 验证码用途
     * 如：REGISTER(注册)、LOGIN(登录)、RESET_PASSWORD(重置密码)等
     */
    private String purpose;
    
    /**
     * 验证码类型枚举
     */
    public enum Purpose {
        REGISTER("注册"),
        LOGIN("登录"),
        RESET_PASSWORD("重置密码"),
        CHANGE_EMAIL("更换邮箱"),
        INTERVIEW_INVITE("面试邀请");
        
        private final String description;
        
        Purpose(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查验证码是否有效
     * 
     * @return true: 有效，false: 无效
     */
    public boolean isValid() {
        return !used && LocalDateTime.now().isBefore(expireTime);
    }
    
    /**
     * 检查验证码是否过期
     * 
     * @return true: 已过期，false: 未过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 标记验证码为已使用
     */
    public void markAsUsed() {
        this.used = true;
    }
    
    /**
     * 创建验证码实例的便捷方法
     * 
     * @param code 验证码内容
     * @param email 邮箱地址
     * @param expireMinutes 过期时间（分钟）
     * @param purpose 验证码用途
     * @return 验证码实例
     */
    public static VerificationCode create(String code, String email, int expireMinutes, String purpose) {
        LocalDateTime now = LocalDateTime.now();
        return new VerificationCode(
                code,
                email,
                now,
                now.plusMinutes(expireMinutes),
                false,
                purpose
        );
    }
}
