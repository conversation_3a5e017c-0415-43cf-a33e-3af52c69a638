package cloud.ipanda.jobplusv8.interceptor;

import cloud.ipanda.jobplusv8.entity.PointsConsumption;
import cloud.ipanda.jobplusv8.service.PointsService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 积分拦截器
 * 
 * 功能说明：
 * 1. 拦截需要消费积分的API请求
 * 2. 检查用户积分是否足够
 * 3. 在积分不足时返回错误响应
 * 4. 支持配置不同功能的积分消费规则
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Component
public class PointsInterceptor implements HandlerInterceptor {

    @Autowired
    private PointsService pointsService;

    @Autowired
    private JwtUtil jwtUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        log.debug("【积分拦截器】检查请求: {} {}", method, requestURI);

        try {
            // 获取当前用户ID
            Long userId = jwtUtil.getCurrentUserIdFromRequest(request);
            if (userId == null) {
                log.warn("【积分拦截器】无法获取用户ID，跳过积分检查");
                return true; // 让安全拦截器处理认证问题
            }

            // 判断是否需要消费积分
            PointsConsumptionRule rule = getConsumptionRule(requestURI, method);
            if (rule == null) {
                log.debug("【积分拦截器】请求不需要消费积分: {}", requestURI);
                return true; // 不需要消费积分，直接通过
            }

            // 检查积分是否足够
            boolean hasEnough = pointsService.hasEnoughPoints(userId, rule.getPointsRequired());
            if (!hasEnough) {
                log.warn("【积分拦截器】用户积分不足，用户ID: {}, 需要积分: {}, 功能: {}", 
                        userId, rule.getPointsRequired(), rule.getFunctionName());
                
                // 返回积分不足的错误响应
                sendInsufficientPointsResponse(response, rule);
                return false;
            }

            // 将消费规则存储到请求属性中，供后续使用
            request.setAttribute("pointsConsumptionRule", rule);
            request.setAttribute("currentUserId", userId);

            log.debug("【积分拦截器】积分检查通过，用户ID: {}, 功能: {}", userId, rule.getFunctionName());
            return true;

        } catch (Exception e) {
            log.error("【积分拦截器】处理异常: {}", e.getMessage(), e);
            // 发生异常时，为了不影响正常流程，允许请求通过
            return true;
        }
    }

    /**
     * 根据请求路径和方法获取积分消费规则
     */
    private PointsConsumptionRule getConsumptionRule(String requestURI, String method) {
        // 面试功能
        if (requestURI.startsWith("/api/interview/") && "POST".equals(method)) {
            if (requestURI.contains("/create") || requestURI.contains("/start")) {
                return new PointsConsumptionRule(
                    PointsConsumption.ConsumptionType.INTERVIEW,
                    10L,
                    "面试功能",
                    "启动面试会话"
                );
            }
        }

        // 语音识别功能 - 按时长消费，不在此处预检查
        // 语音识别按1秒=1积分的规则，在实际使用时扣除
        // 这里不做预检查，因为不知道用户会使用多长时间

        // AI答复功能（如果有相关接口）
        if (requestURI.startsWith("/api/ai/") && "POST".equals(method)) {
            if (requestURI.contains("/reply") || requestURI.contains("/chat")) {
                return new PointsConsumptionRule(
                    PointsConsumption.ConsumptionType.AI_REPLY,
                    3L,
                    "AI答复",
                    "AI智能回复"
                );
            }
        }

        return null; // 不需要消费积分
    }

    /**
     * 发送积分不足的错误响应
     */
    private void sendInsufficientPointsResponse(HttpServletResponse response, PointsConsumptionRule rule) throws IOException {
        response.setStatus(HttpServletResponse.SC_PAYMENT_REQUIRED); // 402 Payment Required
        response.setContentType("application/json;charset=UTF-8");

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("code", 402);
        errorResponse.put("message", String.format("积分不足，使用%s功能需要%d积分，请先充值", 
                                                  rule.getFunctionName(), rule.getPointsRequired()));
        errorResponse.put("data", null);
        
        Map<String, Object> details = new HashMap<>();
        details.put("functionName", rule.getFunctionName());
        details.put("pointsRequired", rule.getPointsRequired());
        details.put("consumptionType", rule.getConsumptionType());
        errorResponse.put("details", details);

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
    }

    /**
     * 积分消费规则内部类
     */
    public static class PointsConsumptionRule {
        private final Integer consumptionType;
        private final Long pointsRequired;
        private final String functionName;
        private final String description;

        public PointsConsumptionRule(Integer consumptionType, Long pointsRequired, 
                                   String functionName, String description) {
            this.consumptionType = consumptionType;
            this.pointsRequired = pointsRequired;
            this.functionName = functionName;
            this.description = description;
        }

        public Integer getConsumptionType() {
            return consumptionType;
        }

        public Long getPointsRequired() {
            return pointsRequired;
        }

        public String getFunctionName() {
            return functionName;
        }

        public String getDescription() {
            return description;
        }
    }
}
