package cloud.ipanda.jobplusv8.listener;

import com.google.gson.Gson;
import com.tencent.asrv2.SpeechRecognizerListener;
import com.tencent.asrv2.SpeechRecognizerResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 腾讯云语音识别监听器
 *
 * 功能说明：
 * 1. 继承腾讯云语音识别SDK的SpeechRecognizerListener类
 * 2. 处理语音识别过程中的各种事件回调
 * 3. 记录识别结果和状态变化，便于调试和监控
 * 4. 支持实时语音识别的完整生命周期管理
 *
 * 事件处理流程：
 * 1. onRecognitionStart - 识别开始
 * 2. onSentenceBegin - 句子开始识别
 * 3. onRecognitionResultChange - 识别结果变化(非稳态)
 * 4. onSentenceEnd - 句子识别结束(稳态结果)
 * 5. onRecognitionComplete - 整个识别过程完成
 * 6. onFail - 识别失败处理
 * 7. onMessage - 所有消息的统一处理入口
 *
 * 技术特点：
 * - 使用prototype作用域，每个WebSocket连接创建独立实例
 * - 详细的日志记录，包含语音ID和完整响应数据
 * - JSON格式化输出，便于调试和数据分析
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@Component  // Spring组件注解，将此类注册为Spring Bean
@Scope("prototype")  // 原型作用域，每次注入时创建新实例，确保多个WebSocket连接间的隔离
public class TencentSpeechRecognizerListener extends SpeechRecognizerListener {

    /**
     * 日志记录器，用于记录语音识别过程中的各种事件和状态
     */
    private static final Logger logger = LoggerFactory.getLogger(TencentSpeechRecognizerListener.class);

    /**
     * JSON序列化工具，用于将响应对象转换为可读的JSON字符串
     */
    private static final Gson gson = new Gson();

    /**
     * 语音识别结果变化事件处理
     *
     * 功能说明：
     * 1. 在语音识别过程中，当识别结果发生变化时触发
     * 2. 此时的识别结果为非稳态，可能会继续变化
     * 3. 适用于实时显示识别进度，提供即时反馈
     *
     * 应用场景：
     * - 实时字幕显示
     * - 语音输入提示
     * - 识别进度监控
     *
     * @param response 语音识别响应对象，包含当前的识别结果和状态信息
     */
    @Override
    public void onRecognitionResultChange(SpeechRecognizerResponse response) {
        logger.info("【识别结果变化】voice_id: {}, 响应数据: {}",
                   response.getVoiceId(), gson.toJson(response));
    }

    /**
     * 语音识别开始事件处理
     *
     * 功能说明：
     * 1. 当语音识别服务开始处理音频流时触发
     * 2. 标志着识别会话的正式开始
     * 3. 可用于初始化识别相关的资源和状态
     *
     * 应用场景：
     * - 初始化识别会话
     * - 记录识别开始时间
     * - 准备结果存储容器
     *
     * @param response 语音识别响应对象，包含识别会话的基本信息
     */
    @Override
    public void onRecognitionStart(SpeechRecognizerResponse response) {
        logger.info("【识别开始】voice_id: {}, 响应数据: {}",
                   response.getVoiceId(), gson.toJson(response));
    }

    /**
     * 句子开始识别事件处理
     *
     * 功能说明：
     * 1. 当检测到一个新句子开始时触发
     * 2. 基于VAD(Voice Activity Detection)技术检测语音活动
     * 3. 标志着一个语音片段的开始
     *
     * 应用场景：
     * - 句子级别的识别统计
     * - 语音片段分割
     * - 实时转录显示
     *
     * @param response 语音识别响应对象，包含句子开始的相关信息
     */
    @Override
    public void onSentenceBegin(SpeechRecognizerResponse response) {
        logger.info("【句子开始】voice_id: {}, 响应数据: {}",
                   response.getVoiceId(), gson.toJson(response));
    }

    /**
     * 句子识别结束事件处理
     *
     * 功能说明：
     * 1. 当一个句子的识别完成时触发
     * 2. 此时的识别结果为稳态，不会再发生变化
     * 3. 可以安全地使用这个结果进行后续处理
     *
     * 重要特性：
     * - 结果稳定性：voice_text_str字段包含最终的识别文本
     * - 完整性：包含完整的句子识别结果
     * - 可靠性：适合用于数据存储和业务处理
     *
     * 应用场景：
     * - 保存识别结果到数据库
     * - 触发业务逻辑处理
     * - 生成面试记录
     * - 实时转录文本输出
     *
     * @param response 语音识别响应对象，包含稳态的识别结果
     */
    @Override
    public void onSentenceEnd(SpeechRecognizerResponse response) {
        logger.info("【句子结束】voice_id: {}, 响应数据: {}",
                   response.getVoiceId(), gson.toJson(response));
    }

    /**
     * 语音识别完成事件处理
     *
     * 功能说明：
     * 1. 当整个语音识别会话结束时触发
     * 2. 标志着当前识别任务的完全结束
     * 3. 可用于清理资源和生成最终报告
     *
     * 处理内容：
     * - 识别会话统计信息
     * - 最终结果汇总
     * - 资源清理准备
     * - 会话结束日志
     *
     * 应用场景：
     * - 生成识别会话报告
     * - 清理临时资源
     * - 统计识别性能
     * - 触发后续流程
     *
     * @param response 语音识别响应对象，包含会话结束的相关信息
     */
    @Override
    public void onRecognitionComplete(SpeechRecognizerResponse response) {
        logger.info("【识别完成】voice_id: {}, 响应数据: {}",
                   response.getVoiceId(), gson.toJson(response));
    }

    /**
     * 语音识别失败事件处理
     *
     * 功能说明：
     * 1. 当语音识别过程中发生错误时触发
     * 2. 提供详细的错误信息和错误代码
     * 3. 支持错误恢复和重试机制
     *
     * 错误类型：
     * - 网络连接错误
     * - 音频格式不支持
     * - 认证失败
     * - 服务限流
     * - 音频质量问题
     *
     * 处理策略：
     * - 记录详细错误信息
     * - 通知客户端错误状态
     * - 触发重试机制(如果适用)
     * - 清理相关资源
     *
     * @param response 语音识别响应对象，包含错误信息和错误代码
     */
    @Override
    public void onFail(SpeechRecognizerResponse response) {
        logger.error("【识别失败】voice_id: {}, 响应数据: {}",
                    response.getVoiceId(), gson.toJson(response));
    }

    /**
     * 通用消息处理事件
     *
     * 功能说明：
     * 1. 处理所有类型的语音识别响应消息
     * 2. 包含稳态和非稳态的识别结果
     * 3. 提供统一的消息处理入口
     *
     * 消息类型：
     * - 识别结果更新消息
     * - 状态变化通知
     * - 错误信息
     * - 系统通知
     *
     * 应用场景：
     * - 统一消息日志记录
     * - 消息路由和分发
     * - 调试和监控
     * - 数据统计分析
     *
     * 注意事项：
     * - 此方法会接收所有消息，包括其他回调方法已处理的消息
     * - 适合用于全局监控和日志记录
     * - 避免在此方法中进行重复的业务处理
     *
     * @param response 语音识别响应对象，包含各种类型的消息数据
     */
    @Override
    public void onMessage(SpeechRecognizerResponse response) {
        logger.debug("【收到消息】voice_id: {}, 响应数据: {}",
                    response.getVoiceId(), gson.toJson(response));
    }
}
