package cloud.ipanda.jobplusv8.mapper;

import cloud.ipanda.jobplusv8.entity.PointsConsumption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
 * 积分消费记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface PointsConsumptionMapper extends BaseMapper<PointsConsumption> {

    /**
     * 统计用户总消费积分
     * 
     * @param userId 用户ID
     * @return 总消费积分
     */
    @Select("SELECT COALESCE(SUM(points_amount), 0) FROM user_points_consumption " +
            "WHERE user_id = #{userId} AND status = 1 AND deleted = 0")
    Long getTotalConsumptionPoints(@Param("userId") Long userId);

    /**
     * 统计指定类型的消费积分
     * 
     * @param userId 用户ID
     * @param consumptionType 消费类型
     * @return 消费积分
     */
    @Select("SELECT COALESCE(SUM(points_amount), 0) FROM user_points_consumption " +
            "WHERE user_id = #{userId} AND consumption_type = #{consumptionType} " +
            "AND status = 1 AND deleted = 0")
    Long getConsumptionPointsByType(@Param("userId") Long userId, 
                                   @Param("consumptionType") Integer consumptionType);

    /**
     * 统计指定时间段内的消费次数
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 消费次数
     */
    @Select("SELECT COUNT(*) FROM user_points_consumption " +
            "WHERE user_id = #{userId} AND status = 1 AND deleted = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime}")
    Long getConsumptionCountByPeriod(@Param("userId") Long userId, 
                                    @Param("startTime") LocalDateTime startTime, 
                                    @Param("endTime") LocalDateTime endTime);
}
