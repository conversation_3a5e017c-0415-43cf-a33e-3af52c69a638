package cloud.ipanda.jobplusv8.mapper;

import cloud.ipanda.jobplusv8.entity.PointsRecharge;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 积分充值记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface PointsRechargeMapper extends BaseMapper<PointsRecharge> {

    /**
     * 统计用户总充值积分
     * 
     * @param userId 用户ID
     * @return 总充值积分
     */
    @Select("SELECT COALESCE(SUM(points_amount), 0) FROM user_points_recharge " +
            "WHERE user_id = #{userId} AND status = 1 AND deleted = 0")
    Long getTotalRechargePoints(@Param("userId") Long userId);

    /**
     * 统计用户总充值金额
     * 
     * @param userId 用户ID
     * @return 总充值金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM user_points_recharge " +
            "WHERE user_id = #{userId} AND status = 1 AND deleted = 0")
    BigDecimal getTotalRechargeAmount(@Param("userId") Long userId);

    /**
     * 统计指定时间段内的充值次数
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 充值次数
     */
    @Select("SELECT COUNT(*) FROM user_points_recharge " +
            "WHERE user_id = #{userId} AND status = 1 AND deleted = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime}")
    Long getRechargeCountByPeriod(@Param("userId") Long userId, 
                                  @Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
}
