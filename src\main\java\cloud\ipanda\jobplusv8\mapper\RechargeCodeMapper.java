package cloud.ipanda.jobplusv8.mapper;

import cloud.ipanda.jobplusv8.entity.RechargeCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 充值码Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface RechargeCodeMapper extends BaseMapper<RechargeCode> {

    /**
     * 使用充值码
     * 
     * @param code 充值码
     * @param userId 使用用户ID
     * @return 影响行数
     */
    @Update("UPDATE recharge_code SET status = 1, used_user_id = #{userId}, " +
            "used_time = NOW() WHERE code = #{code} AND status = 0 AND deleted = 0 " +
            "AND (expire_time IS NULL OR expire_time > NOW())")
    int useRechargeCode(@Param("code") String code, @Param("userId") Long userId);

    /**
     * 批量更新过期充值码状态
     * 
     * @return 影响行数
     */
    @Update("UPDATE recharge_code SET status = 2 WHERE status = 0 AND deleted = 0 " +
            "AND expire_time IS NOT NULL AND expire_time <= NOW()")
    int updateExpiredCodes();
}
