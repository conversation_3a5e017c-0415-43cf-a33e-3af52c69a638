package cloud.ipanda.jobplusv8.mapper;

import cloud.ipanda.jobplusv8.entity.Resume;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 简历Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface ResumeMapper extends BaseMapper<Resume> {

    /**
     * 取消用户的所有默认简历设置
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    @Update("UPDATE user_resume SET is_default = 0 WHERE user_id = #{userId} AND deleted = 0")
    int clearDefaultResume(@Param("userId") Long userId);
}
