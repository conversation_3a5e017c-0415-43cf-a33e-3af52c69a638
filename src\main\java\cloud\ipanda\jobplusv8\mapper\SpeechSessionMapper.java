package cloud.ipanda.jobplusv8.mapper;

import cloud.ipanda.jobplusv8.entity.SpeechSession;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 语音识别会话Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Mapper
public interface SpeechSessionMapper extends BaseMapper<SpeechSession> {

    /**
     * 查询用户的会话统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalSessions, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeSessions, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completedSessions, " +
            "SUM(duration) as totalDuration, " +
            "AVG(duration) as avgDuration " +
            "FROM speech_sessions " +
            "WHERE user_id = #{userId} AND deleted = 0")
    Map<String, Object> getUserSessionStats(@Param("userId") Long userId);

    /**
     * 查询指定时间范围内的会话列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话列表
     */
    @Select("SELECT * FROM speech_sessions " +
            "WHERE user_id = #{userId} " +
            "AND start_time >= #{startTime} " +
            "AND start_time <= #{endTime} " +
            "AND deleted = 0 " +
            "ORDER BY start_time DESC")
    List<SpeechSession> getUserSessionsByTimeRange(@Param("userId") Long userId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询活跃会话数量
     * 
     * @return 活跃会话数量
     */
    @Select("SELECT COUNT(*) FROM speech_sessions WHERE status = 1 AND deleted = 0")
    int getActiveSessionCount();

    /**
     * 查询今日会话统计
     * 
     * @return 今日统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as todaySessions, " +
            "SUM(duration) as todayDuration, " +
            "COUNT(DISTINCT user_id) as todayUsers " +
            "FROM speech_sessions " +
            "WHERE DATE(start_time) = CURDATE() AND deleted = 0")
    Map<String, Object> getTodayStats();
}
