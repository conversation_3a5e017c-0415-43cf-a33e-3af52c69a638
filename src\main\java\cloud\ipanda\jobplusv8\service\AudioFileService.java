package cloud.ipanda.jobplusv8.service;

import java.io.IOException;

/**
 * 录音文件管理服务接口
 * 
 * 功能说明：
 * 1. 管理录音文件的存储和读取
 * 2. 提供文件的创建、写入、关闭操作
 * 3. 支持文件的压缩和格式转换
 * 4. 提供文件清理和维护功能
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface AudioFileService {

    /**
     * 创建录音文件
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 文件路径
     * @throws IOException IO异常
     */
    String createAudioFile(String sessionId, Long userId) throws IOException;

    /**
     * 写入音频数据
     * 
     * @param filePath 文件路径
     * @param audioData 音频数据
     * @throws IOException IO异常
     */
    void writeAudioData(String filePath, byte[] audioData) throws IOException;

    /**
     * 关闭录音文件
     * 
     * @param filePath 文件路径
     * @return 文件大小
     * @throws IOException IO异常
     */
    long closeAudioFile(String filePath) throws IOException;

    /**
     * 删除录音文件
     * 
     * @param filePath 文件路径
     * @return 是否成功
     */
    boolean deleteAudioFile(String filePath);

    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    long getFileSize(String filePath);

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 压缩录音文件
     * 
     * @param filePath 原文件路径
     * @return 压缩后文件路径
     * @throws IOException IO异常
     */
    String compressAudioFile(String filePath) throws IOException;

    /**
     * 清理过期的录音文件
     * 
     * @param daysToKeep 保留天数
     * @return 清理的文件数量
     */
    int cleanupExpiredFiles(int daysToKeep);

    /**
     * 获取录音文件的完整路径
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 文件完整路径
     */
    String getAudioFilePath(String sessionId, Long userId);

    /**
     * 获取录音文件存储目录
     * 
     * @return 存储目录路径
     */
    String getAudioStorageDir();
}
