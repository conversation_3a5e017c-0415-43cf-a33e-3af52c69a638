package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.AuditLog;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

/**
 * 审计日志服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface AuditLogService extends IService<AuditLog> {

    /**
     * 记录操作日志
     * 
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param request HTTP请求
     * @param requestParams 请求参数
     * @param responseResult 响应结果
     * @param status 操作状态
     * @param errorMsg 错误信息
     * @param executionTime 执行时长
     */
    void recordLog(String operationType, String operationDesc, HttpServletRequest request,
                   String requestParams, String responseResult, Integer status, 
                   String errorMsg, Long executionTime);

    /**
     * 记录用户登录日志
     * 
     * @param username 用户名
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMsg 错误信息
     * @param executionTime 执行时长
     */
    void recordLoginLog(String username, HttpServletRequest request, boolean success, 
                       String errorMsg, Long executionTime);

    /**
     * 记录用户注册日志
     * 
     * @param username 用户名
     * @param email 邮箱
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMsg 错误信息
     * @param executionTime 执行时长
     */
    void recordRegisterLog(String username, String email, HttpServletRequest request, 
                          boolean success, String errorMsg, Long executionTime);

    /**
     * 记录邮箱验证码发送日志
     * 
     * @param email 邮箱
     * @param type 验证码类型
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMsg 错误信息
     * @param executionTime 执行时长
     */
    void recordEmailCodeLog(String email, String type, HttpServletRequest request, 
                           boolean success, String errorMsg, Long executionTime);

    /**
     * 记录邮箱验证码验证日志
     * 
     * @param email 邮箱
     * @param type 验证码类型
     * @param request HTTP请求
     * @param success 是否成功
     * @param errorMsg 错误信息
     * @param executionTime 执行时长
     */
    void recordEmailVerifyLog(String email, String type, HttpServletRequest request, 
                             boolean success, String errorMsg, Long executionTime);
}
