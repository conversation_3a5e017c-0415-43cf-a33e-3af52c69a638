package cloud.ipanda.jobplusv8.service;

/**
 * 邮箱验证码服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface EmailVerificationService {

    /**
     * 发送验证码
     * 
     * @param email 邮箱地址
     * @param type 验证码类型（register-注册，login-登录，reset-重置密码）
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String email, String type);

    /**
     * 验证验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    boolean verifyCode(String email, String code, String type);

    /**
     * 检查发送间隔
     * 
     * @param email 邮箱地址
     * @param type 验证码类型
     * @return 是否可以发送（true-可以发送，false-需要等待）
     */
    boolean canSendCode(String email, String type);

    /**
     * 获取剩余等待时间（秒）
     * 
     * @param email 邮箱地址
     * @param type 验证码类型
     * @return 剩余等待时间
     */
    long getRemainingWaitTime(String email, String type);
}
