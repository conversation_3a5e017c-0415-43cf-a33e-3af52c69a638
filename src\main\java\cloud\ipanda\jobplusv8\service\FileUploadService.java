package cloud.ipanda.jobplusv8.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件上传服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface FileUploadService {

    /**
     * 上传简历文件
     * 
     * @param file 上传的文件
     * @param userId 用户ID
     * @return 文件存储路径
     * @throws IOException IO异常
     */
    String uploadResumeFile(MultipartFile file, Long userId) throws IOException;

    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    boolean deleteFile(String filePath);

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    long getFileSize(String filePath);

    /**
     * 验证文件类型
     * 
     * @param file 上传的文件
     * @return 是否为支持的文件类型
     */
    boolean isValidFileType(MultipartFile file);

    /**
     * 验证文件大小
     * 
     * @param file 上传的文件
     * @return 是否在允许的大小范围内
     */
    boolean isValidFileSize(MultipartFile file);

    /**
     * 生成唯一文件名
     * 
     * @param originalFilename 原始文件名
     * @param userId 用户ID
     * @return 唯一文件名
     */
    String generateUniqueFilename(String originalFilename, Long userId);

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名
     */
    String getFileExtension(String filename);

    /**
     * 获取MIME类型
     * 
     * @param file 上传的文件
     * @return MIME类型
     */
    String getMimeType(MultipartFile file);
}
