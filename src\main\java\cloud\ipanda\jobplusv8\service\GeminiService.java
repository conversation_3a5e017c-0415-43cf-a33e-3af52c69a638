package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.dto.GeminiRequest;
import cloud.ipanda.jobplusv8.dto.GeminiResponse;
import java.io.File;

/**
 * Gemini AI服务接口
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface GeminiService {

    /**
     * 解析简历文件并结构化
     *
     * @param file 简历文件
     * @return 结构化的简历信息（JSON格式）
     */
    String parseResume(File file);

    /**
     * 解析简历文件（通过Base64）
     *
     * @param fileBase64 文件的Base64编码
     * @param mimeType 文件MIME类型
     * @return 解析结果
     */
    String parseResumeFromBase64(String fileBase64, String mimeType);

    /**
     * 解析简历文本并结构化
     *
     * @param resumeText 简历文本内容
     * @return 结构化的简历信息（JSON格式）
     */
    String parseResumeText(String resumeText);

    /**
     * 从文件中提取文本内容
     *
     * @param file 文件
     * @return 文本内容
     */
    String extractTextFromFile(File file);

    /**
     * 调用Gemini API
     *
     * @param request 请求对象
     * @return 响应对象
     */
    GeminiResponse callGeminiApi(GeminiRequest request);

    /**
     * 构建简历解析请求
     *
     * @param fileBase64 文件的Base64编码
     * @param mimeType 文件MIME类型
     * @return 请求对象
     */
    GeminiRequest buildResumeParseRequest(String fileBase64, String mimeType);

    /**
     * 检查Gemini服务是否可用
     *
     * @return 是否可用
     */
    boolean isServiceAvailable();
}
