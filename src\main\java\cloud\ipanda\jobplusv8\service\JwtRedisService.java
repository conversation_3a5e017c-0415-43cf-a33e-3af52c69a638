package cloud.ipanda.jobplusv8.service;

/**
 * JWT Redis服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface JwtRedisService {
    
    /**
     * 存储JWT Token到Redis
     * 
     * @param userId 用户ID
     * @param token JWT Token
     * @param expireSeconds 过期时间（秒）
     */
    void storeToken(Long userId, String token, long expireSeconds);
    
    /**
     * 从Redis获取JWT Token
     * 
     * @param userId 用户ID
     * @return JWT Token，如果不存在则返回null
     */
    String getToken(Long userId);
    
    /**
     * 验证Token是否存在于Redis中
     * 
     * @param userId 用户ID
     * @param token JWT Token
     * @return 是否存在
     */
    boolean validateToken(Long userId, String token);
    
    /**
     * 删除用户的JWT Token
     * 
     * @param userId 用户ID
     */
    void removeToken(Long userId);
    
    /**
     * 刷新Token过期时间
     * 
     * @param userId 用户ID
     * @param expireSeconds 新的过期时间（秒）
     * @return 是否刷新成功
     */
    boolean refreshTokenExpire(Long userId, long expireSeconds);
    
    /**
     * 获取Token剩余过期时间
     * 
     * @param userId 用户ID
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示不存在
     */
    long getTokenExpire(Long userId);
    
    /**
     * 检查用户是否在线（Token是否存在）
     * 
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(Long userId);
}
