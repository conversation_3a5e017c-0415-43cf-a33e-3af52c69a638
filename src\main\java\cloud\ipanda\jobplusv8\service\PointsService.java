package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.PointsConsumption;

/**
 * 积分服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface PointsService {

    /**
     * 检查用户积分是否足够
     * 
     * @param userId 用户ID
     * @param requiredPoints 需要的积分
     * @return 是否足够
     */
    boolean hasEnoughPoints(Long userId, Long requiredPoints);

    /**
     * 消费积分
     * 
     * @param userId 用户ID
     * @param pointsAmount 消费积分数量
     * @param consumptionType 消费类型
     * @param businessId 业务ID
     * @param businessDesc 业务描述
     * @return 是否成功
     */
    boolean consumePoints(Long userId, Long pointsAmount, Integer consumptionType, 
                         String businessId, String businessDesc);

    /**
     * 退还积分
     * 
     * @param userId 用户ID
     * @param pointsAmount 退还积分数量
     * @param originalConsumptionId 原消费记录ID
     * @param reason 退还原因
     * @return 是否成功
     */
    boolean refundPoints(Long userId, Long pointsAmount, Long originalConsumptionId, String reason);

    /**
     * 获取用户当前积分
     * 
     * @param userId 用户ID
     * @return 当前积分
     */
    Long getUserPoints(Long userId);

    /**
     * 增加用户积分（充值）
     * 
     * @param userId 用户ID
     * @param pointsAmount 增加的积分数量
     * @return 是否成功
     */
    boolean addUserPoints(Long userId, Long pointsAmount);

    /**
     * 扣除用户积分
     * 
     * @param userId 用户ID
     * @param pointsAmount 扣除的积分数量
     * @return 是否成功
     */
    boolean deductUserPoints(Long userId, Long pointsAmount);

    /**
     * 获取积分消费配置
     * 
     * @param consumptionType 消费类型
     * @return 消费积分数量
     */
    Long getConsumptionPoints(Integer consumptionType);
}
