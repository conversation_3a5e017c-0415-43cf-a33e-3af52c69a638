package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.RechargeCode;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值码服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface RechargeCodeService extends IService<RechargeCode> {

    /**
     * 生成充值码
     * 
     * @param pointsAmount 积分数量
     * @param amount 金额
     * @param expireTime 过期时间
     * @param creatorId 创建者ID
     * @param creatorUsername 创建者用户名
     * @param remark 备注
     * @return 充值码信息
     */
    RechargeCode generateRechargeCode(Long pointsAmount, BigDecimal amount, 
                                     LocalDateTime expireTime, Long creatorId, 
                                     String creatorUsername, String remark);

    /**
     * 批量生成充值码
     * 
     * @param count 生成数量
     * @param pointsAmount 积分数量
     * @param amount 金额
     * @param expireTime 过期时间
     * @param creatorId 创建者ID
     * @param creatorUsername 创建者用户名
     * @param remark 备注
     * @return 充值码列表
     */
    List<RechargeCode> batchGenerateRechargeCodes(Integer count, Long pointsAmount, 
                                                 BigDecimal amount, LocalDateTime expireTime, 
                                                 Long creatorId, String creatorUsername, String remark);

    /**
     * 验证充值码
     * 
     * @param code 充值码
     * @return 充值码信息，如果无效返回null
     */
    RechargeCode validateRechargeCode(String code);

    /**
     * 使用充值码
     * 
     * @param code 充值码
     * @param userId 使用用户ID
     * @return 是否成功
     */
    boolean useRechargeCode(String code, Long userId);

    /**
     * 加密充值码
     * 
     * @param originalCode 原始充值码
     * @return 加密后的充值码
     */
    String encryptCode(String originalCode);

    /**
     * 解密充值码
     * 
     * @param encryptedCode 加密的充值码
     * @return 原始充值码
     */
    String decryptCode(String encryptedCode);

    /**
     * 生成原始充值码
     * 
     * @return 原始充值码
     */
    String generateOriginalCode();

    /**
     * 更新过期充值码状态
     * 
     * @return 更新的数量
     */
    int updateExpiredCodes();
}
