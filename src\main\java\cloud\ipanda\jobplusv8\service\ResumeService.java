package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.dto.ResumeResponse;
import cloud.ipanda.jobplusv8.dto.ResumeUpdateRequest;
import cloud.ipanda.jobplusv8.dto.ResumeUploadRequest;
import cloud.ipanda.jobplusv8.entity.Resume;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 简历服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface ResumeService extends IService<Resume> {

    /**
     * 上传简历
     * 
     * @param request 上传请求
     * @param userId 用户ID
     * @return 简历信息
     */
    ResumeResponse uploadResume(ResumeUploadRequest request, Long userId);

    /**
     * 获取用户的简历列表
     * 
     * @param userId 用户ID
     * @param current 当前页
     * @param size 每页大小
     * @return 分页结果
     */
    Page<ResumeResponse> getUserResumes(Long userId, Integer current, Integer size);

    /**
     * 获取用户的所有简历
     * 
     * @param userId 用户ID
     * @return 简历列表
     */
    List<ResumeResponse> getAllUserResumes(Long userId);

    /**
     * 获取简历详情
     * 
     * @param id 简历ID
     * @param userId 用户ID
     * @return 简历信息
     */
    ResumeResponse getResumeById(Long id, Long userId);

    /**
     * 更新简历信息
     * 
     * @param id 简历ID
     * @param request 更新请求
     * @param userId 用户ID
     * @return 更新后的简历信息
     */
    ResumeResponse updateResume(Long id, ResumeUpdateRequest request, Long userId);

    /**
     * 删除简历
     * 
     * @param id 简历ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteResume(Long id, Long userId);

    /**
     * 设置默认简历
     * 
     * @param id 简历ID
     * @param userId 用户ID
     * @return 是否设置成功
     */
    boolean setDefaultResume(Long id, Long userId);

    /**
     * 获取默认简历
     * 
     * @param userId 用户ID
     * @return 默认简历信息
     */
    ResumeResponse getDefaultResume(Long userId);

    /**
     * 下载简历文件
     * 
     * @param id 简历ID
     * @param userId 用户ID
     * @param response HTTP响应
     */
    void downloadResume(Long id, Long userId, HttpServletResponse response);

    /**
     * 重新解析简历
     * 
     * @param id 简历ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean reparseResume(Long id, Long userId);

    // 异步解析方法已移动到 ResumeAsyncService 中

    /**
     * 转换实体为响应DTO
     * 
     * @param resume 简历实体
     * @return 响应DTO
     */
    ResumeResponse convertToResponse(Resume resume);
}
