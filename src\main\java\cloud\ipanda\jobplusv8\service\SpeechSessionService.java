package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.SpeechSession;
import cloud.ipanda.jobplusv8.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 语音识别会话服务接口
 * 
 * 功能说明：
 * 1. 管理语音识别会话的生命周期
 * 2. 处理用户时长验证和扣减
 * 3. 管理录音文件的持久化存储
 * 4. 提供会话状态查询和统计功能
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface SpeechSessionService extends IService<SpeechSession> {

    /**
     * 创建语音识别会话
     * 
     * @param user 用户信息
     * @param websocketSessionId WebSocket会话ID
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 会话信息
     */
    SpeechSession createSession(User user, String websocketSessionId, String clientIp, String userAgent);

    /**
     * 验证用户是否有足够的时长
     * 
     * @param userId 用户ID
     * @return 是否有足够时长
     */
    boolean hasEnoughDuration(Long userId);

    /**
     * 获取用户剩余时长（秒）
     * 
     * @param userId 用户ID
     * @return 剩余时长
     */
    long getRemainingDuration(Long userId);

    /**
     * 结束语音识别会话
     * 
     * @param sessionId 会话ID
     * @param endReason 结束原因
     * @param remarks 备注
     * @return 是否成功
     */
    boolean endSession(String sessionId, String endReason, String remarks);

    /**
     * 更新会话使用时长
     * 
     * @param sessionId 会话ID
     * @param usedDuration 已使用时长（秒）
     * @return 是否成功
     */
    boolean updateSessionDuration(String sessionId, long usedDuration);

    /**
     * 保存录音文件路径
     * 
     * @param sessionId 会话ID
     * @param audioFilePath 录音文件路径
     * @param fileSize 文件大小
     * @return 是否成功
     */
    boolean saveAudioFile(String sessionId, String audioFilePath, long fileSize);

    /**
     * 根据会话ID获取会话信息
     * 
     * @param sessionId 会话ID
     * @return 会话信息
     */
    SpeechSession getBySessionId(String sessionId);

    /**
     * 根据WebSocket会话ID获取会话信息
     * 
     * @param websocketSessionId WebSocket会话ID
     * @return 会话信息
     */
    SpeechSession getByWebsocketSessionId(String websocketSessionId);

    /**
     * 检查并处理超时会话
     * 
     * @return 处理的会话数量
     */
    int handleTimeoutSessions();

    /**
     * 强制结束用户的所有活跃会话
     * 
     * @param userId 用户ID
     * @param reason 结束原因
     * @return 结束的会话数量
     */
    int forceEndUserSessions(Long userId, String reason);

    /**
     * 增加用户语音识别时长
     * 
     * @param userId 用户ID
     * @param additionalDuration 增加的时长（秒）
     * @return 是否成功
     */
    boolean addUserDuration(Long userId, long additionalDuration);

    /**
     * 扣减用户语音识别时长
     * 
     * @param userId 用户ID
     * @param usedDuration 使用的时长（秒）
     * @return 是否成功
     */
    boolean deductUserDuration(Long userId, long usedDuration);
}
