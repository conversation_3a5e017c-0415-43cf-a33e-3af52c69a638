package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.dto.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 用户中心服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface UserCenterService {

    /**
     * 获取用户中心信息
     * 
     * @param userId 用户ID
     * @return 用户中心信息
     */
    UserCenterResponse getUserCenterInfo(Long userId);

    /**
     * 修改密码
     * 
     * @param userId 用户ID
     * @param request 修改密码请求
     * @return 是否成功
     */
    boolean changePassword(Long userId, ChangePasswordRequest request);

    /**
     * 获取充值记录
     * 
     * @param userId 用户ID
     * @param current 当前页
     * @param size 每页大小
     * @return 充值记录分页
     */
    Page<PointsRechargeResponse> getRechargeRecords(Long userId, Integer current, Integer size);

    /**
     * 获取消费记录
     * 
     * @param userId 用户ID
     * @param current 当前页
     * @param size 每页大小
     * @return 消费记录分页
     */
    Page<PointsConsumptionResponse> getConsumptionRecords(Long userId, Integer current, Integer size);

    /**
     * 充值码充值
     * 
     * @param userId 用户ID
     * @param request 充值请求
     * @return 充值结果
     */
    PointsRechargeResponse rechargeByCode(Long userId, RechargeRequest request);

    /**
     * 管理员直充
     * 
     * @param operatorId 操作员ID
     * @param operatorUsername 操作员用户名
     * @param request 充值请求
     * @return 充值结果
     */
    PointsRechargeResponse adminRecharge(Long operatorId, String operatorUsername, AdminRechargeRequest request);
}
