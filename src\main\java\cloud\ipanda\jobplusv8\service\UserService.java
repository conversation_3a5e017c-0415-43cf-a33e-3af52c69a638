package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface UserService extends IService<User> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User getByUsername(String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    User getByEmail(String email);

    /**
     * 注册用户
     * 
     * @param user 用户信息
     * @return 是否成功
     */
    boolean register(User user);

    /**
     * 用户登录
     *
     * @param usernameOrEmail 用户名或邮箱
     * @param password 密码
     * @return 用户信息
     */
    User login(String usernameOrEmail, String password);

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
}
