package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.service.AudioFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.GZIPOutputStream;

/**
 * 录音文件管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Service
public class AudioFileServiceImpl implements AudioFileService {

    @Value("${audio.storage.path:./audio-files}")
    private String audioStoragePath;

    @Value("${audio.storage.max-file-size:104857600}") // 100MB
    private long maxFileSize;

    // 文件输出流缓存
    private final ConcurrentHashMap<String, FileOutputStream> fileStreamCache = new ConcurrentHashMap<>();

    @Override
    public String createAudioFile(String sessionId, Long userId) throws IOException {
        log.info("【创建录音文件】会话ID: {}, 用户ID: {}", sessionId, userId);
        
        // 创建存储目录
        String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String userDir = String.valueOf(userId);
        Path storageDir = Paths.get(audioStoragePath, dateDir, userDir);
        
        if (!Files.exists(storageDir)) {
            Files.createDirectories(storageDir);
            log.debug("【创建存储目录】{}", storageDir);
        }
        
        // 生成文件名
        String fileName = sessionId + "_" + System.currentTimeMillis() + ".wav";
        String filePath = storageDir.resolve(fileName).toString();
        
        // 创建文件
        File audioFile = new File(filePath);
        if (audioFile.createNewFile()) {
            log.info("【创建录音文件成功】文件路径: {}", filePath);
            return filePath;
        } else {
            throw new IOException("无法创建录音文件: " + filePath);
        }
    }

    @Override
    public void writeAudioData(String filePath, byte[] audioData) throws IOException {
        if (audioData == null || audioData.length == 0) {
            return;
        }
        
        FileOutputStream fos = fileStreamCache.computeIfAbsent(filePath, path -> {
            try {
                return new FileOutputStream(path, true); // 追加模式
            } catch (FileNotFoundException e) {
                log.error("【打开录音文件失败】文件路径: {}", path, e);
                return null;
            }
        });
        
        if (fos != null) {
            // 检查文件大小限制
            File file = new File(filePath);
            if (file.length() + audioData.length > maxFileSize) {
                log.warn("【录音文件超出大小限制】文件: {}, 当前大小: {}, 限制: {}", 
                        filePath, file.length(), maxFileSize);
                return;
            }
            
            fos.write(audioData);
            fos.flush();
            
            log.debug("【写入音频数据】文件: {}, 数据大小: {}bytes", filePath, audioData.length);
        }
    }

    @Override
    public long closeAudioFile(String filePath) throws IOException {
        log.info("【关闭录音文件】文件路径: {}", filePath);
        
        FileOutputStream fos = fileStreamCache.remove(filePath);
        if (fos != null) {
            try {
                fos.close();
            } catch (IOException e) {
                log.error("【关闭录音文件失败】文件路径: {}", filePath, e);
                throw e;
            }
        }
        
        long fileSize = getFileSize(filePath);
        log.info("【关闭录音文件成功】文件路径: {}, 大小: {}bytes", filePath, fileSize);
        
        return fileSize;
    }

    @Override
    public boolean deleteAudioFile(String filePath) {
        log.info("【删除录音文件】文件路径: {}", filePath);
        
        // 先关闭文件流
        FileOutputStream fos = fileStreamCache.remove(filePath);
        if (fos != null) {
            try {
                fos.close();
            } catch (IOException e) {
                log.warn("【关闭文件流失败】文件路径: {}", filePath, e);
            }
        }
        
        // 删除文件
        File file = new File(filePath);
        boolean deleted = file.delete();
        
        log.info("【删除录音文件{}】文件路径: {}", deleted ? "成功" : "失败", filePath);
        return deleted;
    }

    @Override
    public long getFileSize(String filePath) {
        File file = new File(filePath);
        return file.exists() ? file.length() : 0L;
    }

    @Override
    public boolean fileExists(String filePath) {
        return new File(filePath).exists();
    }

    @Override
    public String compressAudioFile(String filePath) throws IOException {
        log.info("【压缩录音文件】原文件: {}", filePath);
        
        String compressedFilePath = filePath + ".gz";
        
        try (FileInputStream fis = new FileInputStream(filePath);
             FileOutputStream fos = new FileOutputStream(compressedFilePath);
             GZIPOutputStream gzos = new GZIPOutputStream(fos)) {
            
            byte[] buffer = new byte[8192];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                gzos.write(buffer, 0, len);
            }
        }
        
        long originalSize = getFileSize(filePath);
        long compressedSize = getFileSize(compressedFilePath);
        
        log.info("【压缩录音文件完成】原文件: {}bytes, 压缩后: {}bytes, 压缩率: {:.2f}%", 
                originalSize, compressedSize, (1.0 - (double)compressedSize / originalSize) * 100);
        
        return compressedFilePath;
    }

    @Override
    public int cleanupExpiredFiles(int daysToKeep) {
        log.info("【清理过期录音文件】保留天数: {}", daysToKeep);
        
        int deletedCount = 0;
        Path storagePath = Paths.get(audioStoragePath);
        
        if (!Files.exists(storagePath)) {
            log.warn("【清理过期文件】存储目录不存在: {}", storagePath);
            return 0;
        }
        
        try {
            deletedCount = cleanupDirectory(storagePath.toFile(), daysToKeep);
        } catch (Exception e) {
            log.error("【清理过期文件失败】", e);
        }
        
        log.info("【清理过期录音文件完成】删除文件数: {}", deletedCount);
        return deletedCount;
    }

    @Override
    public String getAudioFilePath(String sessionId, Long userId) {
        String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String userDir = String.valueOf(userId);
        String fileName = sessionId + "_" + System.currentTimeMillis() + ".wav";
        
        return Paths.get(audioStoragePath, dateDir, userDir, fileName).toString();
    }

    @Override
    public String getAudioStorageDir() {
        return audioStoragePath;
    }

    /**
     * 递归清理目录中的过期文件
     */
    private int cleanupDirectory(File directory, int daysToKeep) {
        int deletedCount = 0;
        long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60L * 60L * 1000L);
        
        File[] files = directory.listFiles();
        if (files == null) {
            return 0;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                deletedCount += cleanupDirectory(file, daysToKeep);
                
                // 如果目录为空，删除目录
                if (file.list() != null && file.list().length == 0) {
                    if (file.delete()) {
                        log.debug("【删除空目录】{}", file.getPath());
                    }
                }
            } else if (file.isFile() && file.lastModified() < cutoffTime) {
                if (file.delete()) {
                    deletedCount++;
                    log.debug("【删除过期文件】{}", file.getPath());
                }
            }
        }
        
        return deletedCount;
    }
}
