package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.AuditLog;
import cloud.ipanda.jobplusv8.mapper.AuditLogMapper;
import cloud.ipanda.jobplusv8.service.AuditLogService;
import cloud.ipanda.jobplusv8.util.IpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * 审计日志服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLog> implements AuditLogService {

    @Override
    @Async
    public void recordLog(String operationType, String operationDesc, HttpServletRequest request,
                         String requestParams, String responseResult, Integer status, 
                         String errorMsg, Long executionTime) {
        try {
            AuditLog auditLog = new AuditLog();
            
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() && 
                !"anonymousUser".equals(authentication.getPrincipal())) {
                auditLog.setUsername(authentication.getName());
            }
            
            auditLog.setOperationType(operationType);
            auditLog.setOperationDesc(operationDesc);
            auditLog.setRequestMethod(request.getMethod());
            auditLog.setRequestUrl(request.getRequestURL().toString());
            auditLog.setRequestParams(requestParams);
            auditLog.setResponseResult(responseResult);
            auditLog.setStatus(status);
            auditLog.setErrorMsg(errorMsg);
            auditLog.setClientIp(IpUtil.getClientIp(request));
            auditLog.setUserAgent(request.getHeader("User-Agent"));
            auditLog.setOperationTime(LocalDateTime.now());
            auditLog.setExecutionTime(executionTime);
            
            save(auditLog);
            
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
        }
    }

    @Override
    public void recordLoginLog(String username, HttpServletRequest request, boolean success, 
                              String errorMsg, Long executionTime) {
        String operationDesc = success ? "用户登录成功" : "用户登录失败";
        String responseResult = success ? "登录成功" : "登录失败: " + errorMsg;
        
        recordLog("USER_LOGIN", operationDesc, request, 
                 "username=" + username, responseResult, 
                 success ? 1 : 0, errorMsg, executionTime);
    }

    @Override
    public void recordRegisterLog(String username, String email, HttpServletRequest request, 
                                 boolean success, String errorMsg, Long executionTime) {
        String operationDesc = success ? "用户注册成功" : "用户注册失败";
        String responseResult = success ? "注册成功" : "注册失败: " + errorMsg;
        String requestParams = "username=" + username + ", email=" + email;
        
        recordLog("USER_REGISTER", operationDesc, request, 
                 requestParams, responseResult, 
                 success ? 1 : 0, errorMsg, executionTime);
    }

    @Override
    public void recordEmailCodeLog(String email, String type, HttpServletRequest request, 
                                  boolean success, String errorMsg, Long executionTime) {
        String operationDesc = success ? "邮箱验证码发送成功" : "邮箱验证码发送失败";
        String responseResult = success ? "验证码发送成功" : "验证码发送失败: " + errorMsg;
        String requestParams = "email=" + email + ", type=" + type;
        
        recordLog("EMAIL_CODE_SEND", operationDesc, request, 
                 requestParams, responseResult, 
                 success ? 1 : 0, errorMsg, executionTime);
    }

    @Override
    public void recordEmailVerifyLog(String email, String type, HttpServletRequest request, 
                                    boolean success, String errorMsg, Long executionTime) {
        String operationDesc = success ? "邮箱验证码验证成功" : "邮箱验证码验证失败";
        String responseResult = success ? "验证码验证成功" : "验证码验证失败: " + errorMsg;
        String requestParams = "email=" + email + ", type=" + type;
        
        recordLog("EMAIL_CODE_VERIFY", operationDesc, request, 
                 requestParams, responseResult, 
                 success ? 1 : 0, errorMsg, executionTime);
    }
}
