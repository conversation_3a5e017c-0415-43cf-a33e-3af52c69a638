package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.security.CustomUserDetails;
import cloud.ipanda.jobplusv8.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义用户详情服务
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private UserService userService;

    @Override
    public UserDetails loadUserByUsername(String usernameOrEmail) throws UsernameNotFoundException {
        log.info("加载用户信息: {}", usernameOrEmail);

        User user = null;

        // 判断输入的是邮箱还是用户名
        if (usernameOrEmail.contains("@")) {
            // 包含@符号，按邮箱查询
            user = userService.getByEmail(usernameOrEmail);
            if (user == null) {
                log.warn("邮箱对应的用户不存在: {}", usernameOrEmail);
                throw new UsernameNotFoundException("邮箱对应的用户不存在: " + usernameOrEmail);
            }
        } else {
            // 不包含@符号，按用户名查询
            user = userService.getByUsername(usernameOrEmail);
            if (user == null) {
                log.warn("用户名不存在: {}", usernameOrEmail);
                throw new UsernameNotFoundException("用户名不存在: " + usernameOrEmail);
            }
        }

        // 获取用户权限（这里先返回空列表，后续可以根据角色获取权限）
        List<String> permissions = getUserPermissions(user.getId());
        
        return new CustomUserDetails(user, permissions);
    }

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    private List<String> getUserPermissions(Long userId) {
        // TODO: 这里可以通过查询数据库获取用户的实际权限
        // 目前先使用简单的权限分配逻辑
        List<String> permissions = new ArrayList<>();
        permissions.add("ROLE_USER");

        // 如果是管理员用户（ID为1），添加管理员权限
        if (userId != null && userId == 1L) {
            permissions.add("ROLE_ADMIN");
            permissions.add("user:read");
            permissions.add("user:write");
            permissions.add("user:delete");
        } else {
            // 普通用户只有查看权限
            permissions.add("user:read");
        }

        return permissions;
    }
}
