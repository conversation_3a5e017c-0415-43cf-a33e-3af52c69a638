package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.service.EmailVerificationService;
import cloud.ipanda.jobplusv8.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

/**
 * 邮箱验证码服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
public class EmailVerificationServiceImpl implements EmailVerificationService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.from}")
    private String fromEmail;

    @Value("${spring.mail.from-name}")
    private String fromName;

    @Value("${spring.mail.code-expire-minutes:5}")
    private int codeExpireMinutes;

    @Value("${spring.mail.code-length:6}")
    private int codeLength;

    @Value("${spring.mail.send-interval-seconds:60}")
    private int sendIntervalSeconds;

    private static final String CODE_PREFIX = "email_code:";
    private static final String SEND_TIME_PREFIX = "email_send_time:";

    @Override
    public boolean sendVerificationCode(String email, String type) {
        try {
            // 检查发送间隔
            if (!canSendCode(email, type)) {
                log.warn("邮箱验证码发送过于频繁，email: {}, type: {}", email, type);
                return false;
            }

            // 生成验证码
            String code = generateVerificationCode();

            // 存储验证码到Redis
            String codeKey = getCodeKey(email, type);
            redisUtil.setString(codeKey, code, codeExpireMinutes, TimeUnit.MINUTES);

            // 记录发送时间
            String sendTimeKey = getSendTimeKey(email, type);
            redisUtil.setString(sendTimeKey, String.valueOf(System.currentTimeMillis()), 
                              sendIntervalSeconds, TimeUnit.SECONDS);

            // 发送邮件
            sendEmail(email, code, type);

            log.info("邮箱验证码发送成功，email: {}, type: {}", email, type);
            return true;

        } catch (Exception e) {
            log.error("邮箱验证码发送失败，email: {}, type: {}", email, type, e);
            return false;
        }
    }

    @Override
    public boolean verifyCode(String email, String code, String type) {
        try {
            String codeKey = getCodeKey(email, type);
            String storedCode = redisUtil.getString(codeKey);

            if (storedCode == null) {
                log.warn("验证码不存在或已过期，email: {}, type: {}", email, type);
                return false;
            }

            boolean isValid = storedCode.equals(code);
            if (isValid) {
                // 验证成功后删除验证码
                redisUtil.deleteString(codeKey);
                log.info("邮箱验证码验证成功，email: {}, type: {}", email, type);
            } else {
                log.warn("邮箱验证码验证失败，email: {}, type: {}", email, type);
            }

            return isValid;

        } catch (Exception e) {
            log.error("邮箱验证码验证异常，email: {}, type: {}", email, type, e);
            return false;
        }
    }

    @Override
    public boolean canSendCode(String email, String type) {
        String sendTimeKey = getSendTimeKey(email, type);
        return !redisUtil.hasKey(sendTimeKey);
    }

    @Override
    public long getRemainingWaitTime(String email, String type) {
        String sendTimeKey = getSendTimeKey(email, type);
        return redisUtil.getExpire(sendTimeKey, TimeUnit.SECONDS);
    }

    /**
     * 生成验证码
     * 
     * @return 验证码
     */
    private String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < codeLength; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 发送邮件
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @param type 验证码类型
     */
    private void sendEmail(String email, String code, String type) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(fromEmail, fromName);
            helper.setTo(email);
            helper.setSubject(getEmailSubject(type));

            String htmlContent = getHtmlEmailContent(code, type);
            helper.setText(htmlContent, true); // true 表示发送HTML邮件

            mailSender.send(mimeMessage);

        } catch (MessagingException e) {
            log.error("发送HTML邮件失败，email: {}, type: {}", email, type, e);
            throw new RuntimeException("邮件发送失败", e);
        } catch (Exception e) {
            log.error("发送邮件异常，email: {}, type: {}", email, type, e);
            throw new RuntimeException("邮件发送异常", e);
        }
    }

    /**
     * 获取邮件主题
     * 
     * @param type 验证码类型
     * @return 邮件主题
     */
    private String getEmailSubject(String type) {
        switch (type) {
            case "register":
                return "【" + fromName + "】注册验证码";
            case "login":
                return "【" + fromName + "】登录验证码";
            case "reset":
                return "【" + fromName + "】密码重置验证码";
            default:
                return "【" + fromName + "】验证码";
        }
    }

    /**
     * 获取HTML邮件内容
     *
     * @param code 验证码
     * @param type 验证码类型
     * @return HTML邮件内容
     */
    private String getHtmlEmailContent(String code, String type) {
        String action = getActionName(type);
        String actionColor = getActionColor(type);
        String iconEmoji = getActionIcon(type);

        return String.format(
            "<!DOCTYPE html>" +
            "<html lang=\"zh-CN\">" +
            "<head>" +
            "    <meta charset=\"UTF-8\">" +
            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
            "    <title>%s验证码</title>" +
            "    <style>" +
            "        * { margin: 0; padding: 0; box-sizing: border-box; }" +
            "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f7fa; padding: 20px; }" +
            "        .email-container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden; }" +
            "        .header { background: linear-gradient(135deg, %s, %s); color: white; padding: 30px 20px; text-align: center; }" +
            "        .header h1 { font-size: 28px; margin-bottom: 10px; font-weight: 600; }" +
            "        .header p { font-size: 16px; opacity: 0.9; }" +
            "        .content { padding: 40px 30px; }" +
            "        .greeting { font-size: 18px; color: #333; margin-bottom: 25px; line-height: 1.6; }" +
            "        .code-section { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 12px; padding: 30px; text-align: center; margin: 30px 0; border: 2px dashed %s; }" +
            "        .code-label { font-size: 16px; color: #666; margin-bottom: 15px; }" +
            "        .verification-code { font-size: 36px; font-weight: bold; color: %s; letter-spacing: 8px; font-family: 'Courier New', monospace; margin: 15px 0; text-shadow: 1px 1px 2px rgba(0,0,0,0.1); }" +
            "        .code-tips { font-size: 14px; color: #888; margin-top: 15px; }" +
            "        .info-box { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 25px 0; }" +
            "        .info-box .icon { font-size: 20px; margin-right: 8px; }" +
            "        .info-box .text { color: #856404; font-size: 14px; line-height: 1.5; }" +
            "        .footer { background-color: #f8f9fa; padding: 25px 30px; border-top: 1px solid #e9ecef; text-align: center; }" +
            "        .footer p { color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 10px; }" +
            "        .company-name { color: %s; font-weight: 600; }" +
            "        .divider { height: 1px; background: linear-gradient(to right, transparent, #ddd, transparent); margin: 25px 0; }" +
            "        @media (max-width: 600px) {" +
            "            .email-container { margin: 10px; border-radius: 8px; }" +
            "            .header { padding: 25px 15px; }" +
            "            .content { padding: 30px 20px; }" +
            "            .verification-code { font-size: 28px; letter-spacing: 4px; }" +
            "        }" +
            "    </style>" +
            "</head>" +
            "<body>" +
            "    <div class=\"email-container\">" +
            "        <div class=\"header\">" +
            "            <h1>%s %s</h1>" +
            "            <p>安全验证码</p>" +
            "        </div>" +
            "        <div class=\"content\">" +
            "            <div class=\"greeting\">" +
            "                <strong>尊敬的用户，您好！</strong><br>" +
            "                您正在进行<strong style=\"color: %s;\">%s</strong>操作，为了保障您的账户安全，请使用以下验证码完成验证。" +
            "            </div>" +
            "            <div class=\"code-section\">" +
            "                <div class=\"code-label\">您的验证码是：</div>" +
            "                <div class=\"verification-code\">%s</div>" +
            "                <div class=\"code-tips\">请在 <strong>%d 分钟</strong> 内使用</div>" +
            "            </div>" +
            "            <div class=\"info-box\">" +
            "                <div class=\"icon\">⚠️</div>" +
            "                <div class=\"text\">" +
            "                    <strong>安全提示：</strong><br>" +
            "                    • 验证码仅用于本次操作，请勿泄露给他人<br>" +
            "                    • 如果这不是您本人的操作，请立即忽略此邮件<br>" +
            "                    • 为了您的账户安全，请定期更换密码" +
            "                </div>" +
            "            </div>" +
            "            <div class=\"divider\"></div>" +
            "            <p style=\"color: #888; font-size: 14px; text-align: center;\">" +
            "                如有疑问，请联系我们的客服团队<br>" +
            "                感谢您使用我们的服务！" +
            "            </p>" +
            "        </div>" +
            "        <div class=\"footer\">" +
            "            <p>此邮件由系统自动发送，请勿直接回复</p>" +
            "            <p>© 2025 <span class=\"company-name\">%s</span> 版权所有</p>" +
            "        </div>" +
            "    </div>" +
            "</body>" +
            "</html>",
            action, // title
            actionColor, getLighterColor(actionColor), // header gradient colors
            actionColor, // border color
            actionColor, // code color
            actionColor, // company name color
            iconEmoji, fromName, // header content
            actionColor, action, // greeting
            code, // verification code
            codeExpireMinutes, // expiry time
            fromName // footer company name
        );
    }

    /**
     * 获取操作名称
     *
     * @param type 验证码类型
     * @return 操作名称
     */
    private String getActionName(String type) {
        switch (type) {
            case "register":
                return "账号注册";
            case "login":
                return "账号登录";
            case "reset":
                return "密码重置";
            default:
                return "身份验证";
        }
    }

    /**
     * 获取操作对应的颜色
     *
     * @param type 验证码类型
     * @return 颜色值
     */
    private String getActionColor(String type) {
        switch (type) {
            case "register":
                return "#28a745"; // 绿色 - 注册
            case "login":
                return "#007bff"; // 蓝色 - 登录
            case "reset":
                return "#dc3545"; // 红色 - 重置密码
            default:
                return "#6c757d"; // 灰色 - 默认
        }
    }

    /**
     * 获取操作对应的图标
     *
     * @param type 验证码类型
     * @return 图标emoji
     */
    private String getActionIcon(String type) {
        switch (type) {
            case "register":
                return "🎉"; // 注册
            case "login":
                return "🔐"; // 登录
            case "reset":
                return "🔄"; // 重置密码
            default:
                return "✉️"; // 默认
        }
    }

    /**
     * 获取较浅的颜色（用于渐变效果）
     *
     * @param color 原始颜色
     * @return 较浅的颜色
     */
    private String getLighterColor(String color) {
        switch (color) {
            case "#28a745":
                return "#34ce57"; // 浅绿色
            case "#007bff":
                return "#339eff"; // 浅蓝色
            case "#dc3545":
                return "#e85d6d"; // 浅红色
            case "#6c757d":
                return "#8d959f"; // 浅灰色
            default:
                return "#8d959f";
        }
    }

    /**
     * 获取验证码存储键
     * 
     * @param email 邮箱地址
     * @param type 验证码类型
     * @return Redis键
     */
    private String getCodeKey(String email, String type) {
        return CODE_PREFIX + type + ":" + email;
    }

    /**
     * 获取发送时间存储键
     * 
     * @param email 邮箱地址
     * @param type 验证码类型
     * @return Redis键
     */
    private String getSendTimeKey(String email, String type) {
        return SEND_TIME_PREFIX + type + ":" + email;
    }
}
