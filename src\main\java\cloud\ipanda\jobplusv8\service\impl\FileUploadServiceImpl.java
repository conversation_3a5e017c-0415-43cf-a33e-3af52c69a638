package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${file.upload.resume.path:./uploads/resumes}")
    private String resumeUploadPath;

    @Value("${file.upload.resume.max-size:10485760}") // 10MB
    private long maxFileSize;

    // 支持的文件类型
    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList("pdf", "doc", "docx", "txt");
    
    // 支持的MIME类型
    private static final List<String> ALLOWED_MIME_TYPES = Arrays.asList(
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain"
    );

    @Override
    public String uploadResumeFile(MultipartFile file, Long userId) throws IOException {
        log.info("【上传简历文件】用户ID: {}, 文件名: {}, 大小: {}bytes", 
                userId, file.getOriginalFilename(), file.getSize());

        // 验证文件
        if (!isValidFileType(file)) {
            throw new IllegalArgumentException("不支持的文件类型，仅支持PDF、Word、TXT格式");
        }

        if (!isValidFileSize(file)) {
            throw new IllegalArgumentException("文件大小超出限制，最大允许" + (maxFileSize / 1024 / 1024) + "MB");
        }

        // 创建存储目录
        String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String userDir = String.valueOf(userId);
        Path storageDir = Paths.get(resumeUploadPath, dateDir, userDir);

        if (!Files.exists(storageDir)) {
            Files.createDirectories(storageDir);
            log.debug("【创建存储目录】{}", storageDir);
        }

        // 生成唯一文件名
        String uniqueFilename = generateUniqueFilename(file.getOriginalFilename(), userId);
        Path filePath = storageDir.resolve(uniqueFilename);

        // 保存文件
        file.transferTo(filePath.toFile());
        
        String savedFilePath = filePath.toString();
        log.info("【上传简历文件成功】文件路径: {}", savedFilePath);
        
        return savedFilePath;
    }

    @Override
    public boolean deleteFile(String filePath) {
        log.info("【删除文件】文件路径: {}", filePath);
        
        File file = new File(filePath);
        boolean deleted = file.delete();
        
        log.info("【删除文件{}】文件路径: {}", deleted ? "成功" : "失败", filePath);
        return deleted;
    }

    @Override
    public boolean fileExists(String filePath) {
        return new File(filePath).exists();
    }

    @Override
    public long getFileSize(String filePath) {
        File file = new File(filePath);
        return file.exists() ? file.length() : 0L;
    }

    @Override
    public boolean isValidFileType(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            return false;
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        String mimeType = file.getContentType();

        return ALLOWED_FILE_TYPES.contains(extension) && 
               (mimeType == null || ALLOWED_MIME_TYPES.contains(mimeType));
    }

    @Override
    public boolean isValidFileSize(MultipartFile file) {
        return file != null && file.getSize() <= maxFileSize && file.getSize() > 0;
    }

    @Override
    public String generateUniqueFilename(String originalFilename, Long userId) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        long timestamp = System.currentTimeMillis();
        
        return String.format("%d_%s_%d.%s", userId, uuid, timestamp, extension);
    }

    @Override
    public String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        
        return filename.substring(lastDotIndex + 1);
    }

    @Override
    public String getMimeType(MultipartFile file) {
        return file.getContentType();
    }
}
