package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.config.GeminiConfig;
import cloud.ipanda.jobplusv8.dto.GeminiRequest;
import cloud.ipanda.jobplusv8.dto.GeminiResponse;
import cloud.ipanda.jobplusv8.service.GeminiService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

/**
 * Gemini AI服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class GeminiServiceImpl implements GeminiService {

    @Autowired
    private GeminiConfig geminiConfig;

    @Value("${gemini.api.key:}")
    private String apiKey;

    @Value("${gemini.api.url:https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent}")
    private String apiUrl;

    @Value("${gemini.enabled:false}")
    private boolean geminiEnabled;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String parseResume(File file) {
        String taskId = "FILE_PARSE_" + System.currentTimeMillis();
        long startTime = System.currentTimeMillis();

        log.info("【文件解析开始】任务ID: {}, 文件: {}, 大小: {}KB",
                taskId, file.getName(), file.length() / 1024);

        if (!geminiEnabled) {
            log.warn("【文件解析】任务ID: {}, Gemini服务未启用，返回模拟数据", taskId);
            return getMockResumeData();
        }

        try {
            // 检查文件是否存在
            if (!file.exists()) {
                log.error("【文件解析失败】任务ID: {}, 文件不存在: {}", taskId, file.getAbsolutePath());
                throw new RuntimeException("文件不存在: " + file.getAbsolutePath());
            }

            if (!file.canRead()) {
                log.error("【文件解析失败】任务ID: {}, 文件无法读取: {}", taskId, file.getAbsolutePath());
                throw new RuntimeException("文件无法读取: " + file.getAbsolutePath());
            }

            // 直接将文件发送给Gemini进行解析，不需要预先提取文本
            log.info("【文件解析】任务ID: {}, 开始转换文件为Base64...", taskId);
            String fileBase64 = fileToBase64(file);
            String mimeType = getMimeType(file);

            log.info("【文件解析】任务ID: {}, 文件转换完成 - 类型: {}, Base64长度: {}, 估算原始大小: {}KB",
                    taskId, mimeType, fileBase64.length(), (long)(fileBase64.length() * 0.75) / 1024);

            // 调用Gemini API解析
            log.info("【文件解析】任务ID: {}, 开始调用Gemini解析...", taskId);
            String result = parseResumeFromBase64(fileBase64, mimeType);

            long duration = System.currentTimeMillis() - startTime;
            if (result != null) {
                log.info("【文件解析成功】任务ID: {}, 文件: {}, 总耗时: {}ms",
                        taskId, file.getName(), duration);
            } else {
                log.warn("【文件解析完成但结果为空】任务ID: {}, 文件: {}, 总耗时: {}ms",
                        taskId, file.getName(), duration);
            }

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("【文件解析异常】任务ID: {}, 文件: {}, 耗时: {}ms, 异常: {}",
                    taskId, file.getName(), duration, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String parseResumeText(String resumeText) {
        String taskId = "TEXT_PARSE_" + System.currentTimeMillis();
        long startTime = System.currentTimeMillis();

        log.info("【文本解析开始】任务ID: {}, 文本长度: {}字符", taskId, resumeText.length());
        log.debug("【文本解析内容】任务ID: {}, 文本预览: {}",
                taskId, resumeText.length() > 200 ? resumeText.substring(0, 200) + "..." : resumeText);

        if (!geminiEnabled) {
            log.warn("【文本解析】任务ID: {}, Gemini服务未启用，返回模拟数据", taskId);
            return getMockResumeData();
        }

        try {
            // 使用新的DTO方式构建文本解析请求
            log.info("【文本解析】任务ID: {}, 开始构建文本解析请求...", taskId);
            GeminiRequest request = buildTextParseRequest(resumeText);
            log.info("【文本解析】任务ID: {}, 文本解析请求构建完成", taskId);

            // 调用API
            log.info("【文本解析】任务ID: {}, 开始调用Gemini API...", taskId);
            GeminiResponse response = callGeminiApi(request);

            // 提取解析结果
            log.info("【文本解析】任务ID: {}, 开始提取解析结果...", taskId);
            if (response.getCandidates() != null && !response.getCandidates().isEmpty()) {
                GeminiResponse.Candidate candidate = response.getCandidates().get(0);
                if (candidate.getContent() != null && candidate.getContent().getParts() != null && !candidate.getContent().getParts().isEmpty()) {
                    String result = candidate.getContent().getParts().get(0).getText();
                    long duration = System.currentTimeMillis() - startTime;

                    log.info("【文本解析成功】任务ID: {}, 结果长度: {}字符, 总耗时: {}ms",
                            taskId, result.length(), duration);
                    log.debug("【文本解析结果预览】任务ID: {}, 内容: {}",
                            taskId, result.length() > 200 ? result.substring(0, 200) + "..." : result);

                    return result;
                }
            }

            log.error("【文本解析失败】任务ID: {}, 原因: Gemini API返回结果为空", taskId);
            throw new RuntimeException("Gemini API返回结果为空");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("【文本解析异常】任务ID: {}, 耗时: {}ms, 异常: {}",
                    taskId, duration, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String extractTextFromFile(File file) {
        log.info("【Gemini解析文件】文件: {}, 大小: {}KB", file.getName(), file.length() / 1024);

        // 现在直接使用Gemini API解析文件，不需要预先提取文本
        // 让Gemini直接处理各种格式的文件
        try {
            String fileBase64 = fileToBase64(file);
            String mimeType = getMimeType(file);

            log.info("【发送文件给Gemini解析】文件类型: {}", mimeType);

            return parseResumeFromBase64(fileBase64, mimeType);

        } catch (Exception e) {
            log.error("【Gemini解析文件失败】文件: {}", file.getName(), e);
            return null;
        }
    }

    @Override
    public String parseResumeFromBase64(String fileBase64, String mimeType) {
        String taskId = "PARSE_" + System.currentTimeMillis();
        long startTime = System.currentTimeMillis();

        log.info("【简历解析开始】任务ID: {}, MIME类型: {}, 文件大小: {}KB",
                taskId, mimeType, (long)(fileBase64.length() * 0.75) / 1024);

        try {
            // 构建请求
            log.info("【简历解析】任务ID: {}, 开始构建Gemini请求...", taskId);
            GeminiRequest request = buildResumeParseRequest(fileBase64, mimeType);
            log.info("【简历解析】任务ID: {}, Gemini请求构建完成", taskId);

            // 调用API
            log.info("【简历解析】任务ID: {}, 开始调用Gemini API...", taskId);
            GeminiResponse response = callGeminiApi(request);

            // 提取解析结果
            log.info("【简历解析】任务ID: {}, 开始提取解析结果...", taskId);
            if (response.getCandidates() != null && !response.getCandidates().isEmpty()) {
                GeminiResponse.Candidate candidate = response.getCandidates().get(0);
                if (candidate.getContent() != null && candidate.getContent().getParts() != null && !candidate.getContent().getParts().isEmpty()) {
                    String result = candidate.getContent().getParts().get(0).getText();
                    long duration = System.currentTimeMillis() - startTime;

                    log.info("【简历解析成功】任务ID: {}, 结果长度: {}字符, 总耗时: {}ms",
                            taskId, result.length(), duration);
                    log.debug("【简历解析结果预览】任务ID: {}, 内容: {}",
                            taskId, result.length() > 200 ? result.substring(0, 200) + "..." : result);

                    return result;
                }
            }

            log.error("【简历解析失败】任务ID: {}, 原因: Gemini API返回结果为空", taskId);
            throw new RuntimeException("Gemini API返回结果为空");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("【简历解析异常】任务ID: {}, 耗时: {}ms, 异常: {}",
                    taskId, duration, e.getMessage(), e);
            throw new RuntimeException("简历解析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public GeminiResponse callGeminiApi(GeminiRequest request) {
        long startTime = System.currentTimeMillis();
        String requestId = "REQ_" + System.currentTimeMillis();

        try {
            String url = geminiConfig.getBaseUrl() + "/v1beta/models/" + geminiConfig.getModel() + ":generateContent";

            // 记录请求开始日志
            log.info("【Gemini API开始】请求ID: {}, URL: {}, 模型: {}",
                    requestId, url, geminiConfig.getModel());

            // 记录请求配置信息
            log.info("【Gemini API配置】请求ID: {}, 温度: {}, 响应类型: {}, 思考预算: {}",
                    requestId,
                    geminiConfig.getTemperature(),
                    geminiConfig.getResponseMimeType(),
                    geminiConfig.getThinkingBudget());

            // 记录请求内容统计
            if (request.getContents() != null && !request.getContents().isEmpty()) {
                GeminiRequest.Content content = request.getContents().get(0);
                if (content.getParts() != null) {
                    int textParts = 0;
                    int fileParts = 0;
                    long totalFileSize = 0;

                    for (GeminiRequest.Part part : content.getParts()) {
                        if (part.getText() != null) {
                            textParts++;
                            log.debug("【Gemini API请求】请求ID: {}, 文本部分长度: {}",
                                    requestId, part.getText().length());
                        }
                        if (part.getInlineData() != null) {
                            fileParts++;
                            String base64Data = part.getInlineData().getData();
                            if (base64Data != null) {
                                // 估算原始文件大小（Base64编码后大约增加33%）
                                long estimatedSize = (long) (base64Data.length() * 0.75);
                                totalFileSize += estimatedSize;
                                log.info("【Gemini API请求】请求ID: {}, 文件类型: {}, Base64长度: {}, 估算文件大小: {}KB",
                                        requestId,
                                        part.getInlineData().getMimeType(),
                                        base64Data.length(),
                                        estimatedSize / 1024);
                            }
                        }
                    }

                    log.info("【Gemini API请求统计】请求ID: {}, 文本部分: {}个, 文件部分: {}个, 总文件大小: {}KB",
                            requestId, textParts, fileParts, totalFileSize / 1024);
                }
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("x-goog-api-key", geminiConfig.getApiKey().substring(0, 8) + "****"); // 脱敏显示

            log.info("【Gemini API请求头】请求ID: {}, Content-Type: {}, API-Key: {}****",
                    requestId, MediaType.APPLICATION_JSON, geminiConfig.getApiKey().substring(0, 8));

            // 重新设置完整的API Key（实际请求用）
            headers.set("x-goog-api-key", geminiConfig.getApiKey());

            HttpEntity<GeminiRequest> entity = new HttpEntity<>(request, headers);

            log.info("【Gemini API发送】请求ID: {}, 开始发送HTTP请求...", requestId);

            // 调用API
            ResponseEntity<GeminiResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, GeminiResponse.class);

            long duration = System.currentTimeMillis() - startTime;

            if (response.getStatusCode() == HttpStatus.OK) {
                GeminiResponse responseBody = response.getBody();

                // 记录响应成功日志
                log.info("【Gemini API成功】请求ID: {}, 状态码: {}, 耗时: {}ms",
                        requestId, response.getStatusCode(), duration);

                // 记录响应详细信息
                if (responseBody != null) {
                    log.info("【Gemini API响应】请求ID: {}, 模型版本: {}, 响应ID: {}",
                            requestId, responseBody.getModelVersion(), responseBody.getResponseId());

                    // 记录候选结果信息
                    if (responseBody.getCandidates() != null && !responseBody.getCandidates().isEmpty()) {
                        GeminiResponse.Candidate candidate = responseBody.getCandidates().get(0);
                        log.info("【Gemini API候选结果】请求ID: {}, 完成原因: {}, 索引: {}",
                                requestId, candidate.getFinishReason(), candidate.getIndex());

                        if (candidate.getContent() != null && candidate.getContent().getParts() != null && !candidate.getContent().getParts().isEmpty()) {
                            String resultText = candidate.getContent().getParts().get(0).getText();
                            log.info("【Gemini API结果】请求ID: {}, 结果长度: {}字符",
                                    requestId, resultText != null ? resultText.length() : 0);
                            log.debug("【Gemini API结果内容】请求ID: {}, 内容预览: {}",
                                    requestId, resultText != null && resultText.length() > 100 ?
                                    resultText.substring(0, 100) + "..." : resultText);
                        }
                    }

                    // 记录使用量信息
                    if (responseBody.getUsageMetadata() != null) {
                        GeminiResponse.UsageMetadata usage = responseBody.getUsageMetadata();
                        log.info("【Gemini API使用量】请求ID: {}, 提示Token: {}, 候选Token: {}, 总Token: {}, 思考Token: {}",
                                requestId,
                                usage.getPromptTokenCount(),
                                usage.getCandidatesTokenCount(),
                                usage.getTotalTokenCount(),
                                usage.getThoughtsTokenCount());
                    }
                }

                log.info("【Gemini API完成】请求ID: {}, 总耗时: {}ms", requestId, duration);
                return responseBody;

            } else {
                log.error("【Gemini API失败】请求ID: {}, 状态码: {}, 耗时: {}ms, 响应头: {}",
                        requestId, response.getStatusCode(), duration, response.getHeaders());

                // 尝试记录错误响应体
                if (response.getBody() != null) {
                    log.error("【Gemini API错误响应】请求ID: {}, 响应体: {}", requestId, response.getBody());
                }

                throw new RuntimeException("Gemini API调用失败: " + response.getStatusCode());
            }

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("【Gemini API异常】请求ID: {}, 耗时: {}ms, 异常类型: {}, 异常信息: {}",
                    requestId, duration, e.getClass().getSimpleName(), e.getMessage(), e);

            // 记录更详细的异常信息
            if (e.getCause() != null) {
                log.error("【Gemini API异常根因】请求ID: {}, 根因类型: {}, 根因信息: {}",
                        requestId, e.getCause().getClass().getSimpleName(), e.getCause().getMessage());
            }

            throw new RuntimeException("Gemini API调用异常: " + e.getMessage(), e);
        }
    }

    @Override
    public GeminiRequest buildResumeParseRequest(String fileBase64, String mimeType) {
        log.debug("【构建简历解析请求】开始构建, MIME类型: {}, Base64长度: {}", mimeType, fileBase64.length());

        // 构建内联数据部分
        GeminiRequest.InlineData inlineData = GeminiRequest.InlineData.builder()
            .data(fileBase64)
            .mimeType(mimeType)
            .build();
        log.debug("【构建简历解析请求】内联数据构建完成");

        // 构建文本部分
        String promptText = "解析附件，并返回简历信息。只返回附件内容，禁止返回跟附件无关内容。";
        GeminiRequest.Part textPart = GeminiRequest.Part.builder()
            .text(promptText)
            .build();
        log.debug("【构建简历解析请求】文本提示构建完成: {}", promptText);

        // 构建文件部分
        GeminiRequest.Part filePart = GeminiRequest.Part.builder()
            .inlineData(inlineData)
            .build();
        log.debug("【构建简历解析请求】文件部分构建完成");

        // 构建内容
        GeminiRequest.Content content = GeminiRequest.Content.builder()
            .parts(Arrays.asList(textPart, filePart))
            .role("user")
            .build();
        log.debug("【构建简历解析请求】内容部分构建完成, 包含{}个部分", content.getParts().size());

        // 构建系统指令
        GeminiRequest.SystemInstruction systemInstruction = GeminiRequest.SystemInstruction.builder()
            .parts(Arrays.asList(GeminiRequest.Part.builder().text("").build()))
            .role("user")
            .build();
        log.debug("【构建简历解析请求】系统指令构建完成");

        // 构建安全设置
        List<GeminiRequest.SafetySetting> safetySettings = Arrays.asList(
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_HATE_SPEECH").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_SEXUALLY_EXPLICIT").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_HARASSMENT").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_DANGEROUS_CONTENT").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_CIVIC_INTEGRITY").threshold("BLOCK_NONE").build()
        );
        log.debug("【构建简历解析请求】安全设置构建完成, 包含{}个设置", safetySettings.size());

        // 构建生成配置
        GeminiRequest.ThinkingConfig thinkingConfig = GeminiRequest.ThinkingConfig.builder()
            .includeThoughts(false)
            .thinkingBudget(geminiConfig.getThinkingBudget())
            .build();

        GeminiRequest.GenerationConfig generationConfig = GeminiRequest.GenerationConfig.builder()
            .temperature(geminiConfig.getTemperature())
            .responseMimeType(geminiConfig.getResponseMimeType())
            .thinkingConfig(thinkingConfig)
            .build();
        log.debug("【构建简历解析请求】生成配置构建完成, 温度: {}, 思考预算: {}",
                geminiConfig.getTemperature(), geminiConfig.getThinkingBudget());

        // 构建完整请求
        GeminiRequest request = GeminiRequest.builder()
            .contents(Arrays.asList(content))
            .systemInstruction(systemInstruction)
            .safetySettings(safetySettings)
            .tools(Collections.emptyList())
            .generationConfig(generationConfig)
            .build();

        log.debug("【构建简历解析请求】完整请求构建完成");
        return request;
    }

    @Override
    public boolean isServiceAvailable() {
        return geminiEnabled && apiKey != null && !apiKey.trim().isEmpty();
    }

    // 注意：以下文本提取方法已不再使用
    // 现在直接将文件发送给Gemini API进行解析，无需预先提取文本
    // Gemini 2.5 Pro 可以直接处理PDF、Word、图片等多种格式的文件

    // 注意：以下旧的请求构建方法已被新的DTO方式替代
    // 现在使用 buildResumeParseRequest() 和 buildTextParseRequest() 方法

    /**
     * 将文件转换为Base64编码
     */
    private String fileToBase64(File file) throws IOException {
        long startTime = System.currentTimeMillis();
        log.debug("【文件转Base64】开始转换文件: {}, 大小: {}KB", file.getName(), file.length() / 1024);

        byte[] fileBytes = Files.readAllBytes(file.toPath());
        String base64 = Base64.getEncoder().encodeToString(fileBytes);

        long duration = System.currentTimeMillis() - startTime;
        log.debug("【文件转Base64】转换完成, 文件: {}, 原始大小: {}KB, Base64长度: {}, 耗时: {}ms",
                file.getName(), file.length() / 1024, base64.length(), duration);

        return base64;
    }

    /**
     * 根据文件扩展名获取MIME类型
     */
    private String getMimeType(File file) {
        String fileName = file.getName().toLowerCase();
        String mimeType;

        if (fileName.endsWith(".pdf")) {
            mimeType = "application/pdf";
        } else if (fileName.endsWith(".docx")) {
            mimeType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (fileName.endsWith(".doc")) {
            mimeType = "application/msword";
        } else if (fileName.endsWith(".txt")) {
            mimeType = "text/plain";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            mimeType = "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            mimeType = "image/png";
        } else {
            mimeType = "application/octet-stream";
            log.warn("【MIME类型识别】未知文件类型: {}, 使用默认类型: {}", fileName, mimeType);
        }

        log.debug("【MIME类型识别】文件: {}, 类型: {}", fileName, mimeType);
        return mimeType;
    }

    /**
     * 构建文本解析请求
     */
    private GeminiRequest buildTextParseRequest(String resumeText) {
        String prompt = "请分析以下简历内容，并以JSON格式返回结构化信息，包括：\n" +
                "1. 基本信息：姓名、电话、邮箱、地址\n" +
                "2. 教育背景：学校、专业、学历、毕业时间\n" +
                "3. 工作经历：公司、职位、工作时间、工作描述\n" +
                "4. 技能特长：技术栈、专业技能\n" +
                "5. 项目经验：项目名称、项目描述、使用技术\n" +
                "6. 其他信息：证书、奖项等\n\n" +
                "简历内容：\n" + resumeText;

        // 构建文本部分
        GeminiRequest.Part textPart = GeminiRequest.Part.builder()
            .text(prompt)
            .build();

        // 构建内容
        GeminiRequest.Content content = GeminiRequest.Content.builder()
            .parts(Arrays.asList(textPart))
            .role("user")
            .build();

        // 构建系统指令
        GeminiRequest.SystemInstruction systemInstruction = GeminiRequest.SystemInstruction.builder()
            .parts(Arrays.asList(GeminiRequest.Part.builder().text("").build()))
            .role("user")
            .build();

        // 构建安全设置
        List<GeminiRequest.SafetySetting> safetySettings = Arrays.asList(
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_HATE_SPEECH").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_SEXUALLY_EXPLICIT").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_HARASSMENT").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_DANGEROUS_CONTENT").threshold("OFF").build(),
            GeminiRequest.SafetySetting.builder().category("HARM_CATEGORY_CIVIC_INTEGRITY").threshold("BLOCK_NONE").build()
        );

        // 构建生成配置
        GeminiRequest.ThinkingConfig thinkingConfig = GeminiRequest.ThinkingConfig.builder()
            .includeThoughts(true)
            .thinkingBudget(geminiConfig.getThinkingBudget())
            .build();

        GeminiRequest.GenerationConfig generationConfig = GeminiRequest.GenerationConfig.builder()
            .temperature(geminiConfig.getTemperature())
            .responseMimeType(geminiConfig.getResponseMimeType())
            .thinkingConfig(thinkingConfig)
            .build();

        // 构建完整请求
        return GeminiRequest.builder()
            .contents(Arrays.asList(content))
            .systemInstruction(systemInstruction)
            .safetySettings(safetySettings)
            .tools(Collections.emptyList())
            .generationConfig(generationConfig)
            .build();
    }

    /**
     * 获取模拟简历数据（用于测试）
     */
    private String getMockResumeData() {
        Map<String, Object> mockData = new HashMap<>();

        Map<String, String> basicInfo = new HashMap<>();
        basicInfo.put("name", "张三");
        basicInfo.put("phone", "13800138000");
        basicInfo.put("email", "<EMAIL>");
        basicInfo.put("address", "北京市朝阳区");
        mockData.put("basicInfo", basicInfo);

        Map<String, String> education = new HashMap<>();
        education.put("school", "北京大学");
        education.put("major", "计算机科学与技术");
        education.put("degree", "本科");
        education.put("graduationTime", "2020-06");
        mockData.put("education", education);

        mockData.put("skills", new String[]{"Java", "Spring Boot", "MySQL", "Redis"});
        mockData.put("parseTime", System.currentTimeMillis());

        try {
            return objectMapper.writeValueAsString(mockData);
        } catch (Exception e) {
            log.error("【生成模拟数据失败】", e);
            return "{}";
        }
    }
}
