package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.service.JwtRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * JWT Redis服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class JwtRedisServiceImpl implements JwtRedisService {
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    /**
     * JWT Token在Redis中的key前缀
     */
    private static final String JWT_TOKEN_PREFIX = "jwt:token:";
    
    /**
     * 构建Redis Key
     * 
     * @param userId 用户ID
     * @return Redis Key
     */
    private String buildTokenKey(Long userId) {
        return JWT_TOKEN_PREFIX + userId;
    }
    
    @Override
    public void storeToken(Long userId, String token, long expireSeconds) {
        String key = buildTokenKey(userId);
        
        try {
            stringRedisTemplate.opsForValue().set(key, token, expireSeconds, TimeUnit.SECONDS);
            log.info("【JWT存储】用户ID: {}, Token存储成功, 过期时间: {}秒", userId, expireSeconds);
            log.debug("【JWT存储】用户ID: {}, Redis Key: {}, Token: {}...", 
                    userId, key, token.length() > 20 ? token.substring(0, 20) + "..." : token);
        } catch (Exception e) {
            log.error("【JWT存储失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("JWT Token存储失败: " + e.getMessage());
        }
    }
    
    @Override
    public String getToken(Long userId) {
        String key = buildTokenKey(userId);
        
        try {
            String token = stringRedisTemplate.opsForValue().get(key);
            if (token != null) {
                log.debug("【JWT获取】用户ID: {}, Token获取成功", userId);
            } else {
                log.debug("【JWT获取】用户ID: {}, Token不存在", userId);
            }
            return token;
        } catch (Exception e) {
            log.error("【JWT获取失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean validateToken(Long userId, String token) {
        if (userId == null || token == null) {
            log.warn("【JWT验证】参数为空, 用户ID: {}, Token: {}", userId, token != null ? "存在" : "null");
            return false;
        }
        
        try {
            String storedToken = getToken(userId);
            boolean isValid = token.equals(storedToken);
            
            log.debug("【JWT验证】用户ID: {}, 验证结果: {}", userId, isValid ? "通过" : "失败");
            if (!isValid) {
                log.warn("【JWT验证失败】用户ID: {}, 原因: Token不匹配", userId);
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("【JWT验证异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public void removeToken(Long userId) {
        String key = buildTokenKey(userId);
        
        try {
            Boolean deleted = stringRedisTemplate.delete(key);
            if (Boolean.TRUE.equals(deleted)) {
                log.info("【JWT删除】用户ID: {}, Token删除成功", userId);
            } else {
                log.warn("【JWT删除】用户ID: {}, Token不存在或删除失败", userId);
            }
        } catch (Exception e) {
            log.error("【JWT删除失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }
    
    @Override
    public boolean refreshTokenExpire(Long userId, long expireSeconds) {
        String key = buildTokenKey(userId);
        
        try {
            Boolean success = stringRedisTemplate.expire(key, expireSeconds, TimeUnit.SECONDS);
            if (Boolean.TRUE.equals(success)) {
                log.info("【JWT刷新】用户ID: {}, 过期时间刷新成功, 新过期时间: {}秒", userId, expireSeconds);
                return true;
            } else {
                log.warn("【JWT刷新】用户ID: {}, 过期时间刷新失败, Token可能不存在", userId);
                return false;
            }
        } catch (Exception e) {
            log.error("【JWT刷新失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public long getTokenExpire(Long userId) {
        String key = buildTokenKey(userId);
        
        try {
            Long expire = stringRedisTemplate.getExpire(key, TimeUnit.SECONDS);
            log.debug("【JWT过期时间】用户ID: {}, 剩余时间: {}秒", userId, expire);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            log.error("【JWT过期时间查询失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return -2;
        }
    }
    
    @Override
    public boolean isUserOnline(Long userId) {
        String key = buildTokenKey(userId);
        
        try {
            Boolean exists = stringRedisTemplate.hasKey(key);
            boolean online = Boolean.TRUE.equals(exists);
            log.debug("【用户在线状态】用户ID: {}, 在线状态: {}", userId, online ? "在线" : "离线");
            return online;
        } catch (Exception e) {
            log.error("【用户在线状态查询失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }
}
