package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.PointsConsumption;
import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.mapper.PointsConsumptionMapper;
import cloud.ipanda.jobplusv8.mapper.UserMapper;
import cloud.ipanda.jobplusv8.service.PointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 积分服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class PointsServiceImpl implements PointsService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PointsConsumptionMapper pointsConsumptionMapper;

    @Override
    public boolean hasEnoughPoints(Long userId, Long requiredPoints) {
        log.info("【检查用户积分】用户ID: {}, 需要积分: {}", userId, requiredPoints);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            log.warn("【用户不存在】用户ID: {}", userId);
            return false;
        }
        
        Long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
        boolean hasEnough = currentPoints >= requiredPoints;
        
        log.info("【积分检查结果】用户ID: {}, 当前积分: {}, 需要积分: {}, 是否足够: {}", 
                userId, currentPoints, requiredPoints, hasEnough);
        
        return hasEnough;
    }

    @Override
    @Transactional
    public boolean consumePoints(Long userId, Long pointsAmount, Integer consumptionType, 
                                String businessId, String businessDesc) {
        log.info("【消费积分】用户ID: {}, 消费积分: {}, 消费类型: {}, 业务ID: {}", 
                userId, pointsAmount, consumptionType, businessId);

        try {
            // 检查积分是否足够
            if (!hasEnoughPoints(userId, pointsAmount)) {
                log.warn("【积分不足】用户ID: {}, 需要积分: {}", userId, pointsAmount);
                return false;
            }

            // 获取用户当前积分
            User user = userMapper.selectById(userId);
            Long pointsBefore = user.getPoints() != null ? user.getPoints() : 0L;
            Long pointsAfter = pointsBefore - pointsAmount;

            // 扣除积分
            user.setPoints(pointsAfter);
            int updateResult = userMapper.updateById(user);
            
            if (updateResult <= 0) {
                log.error("【更新用户积分失败】用户ID: {}", userId);
                return false;
            }

            // 记录消费记录
            PointsConsumption consumption = new PointsConsumption();
            consumption.setUserId(userId);
            consumption.setConsumptionType(consumptionType);
            consumption.setPointsAmount(pointsAmount);
            consumption.setPointsBefore(pointsBefore);
            consumption.setPointsAfter(pointsAfter);
            consumption.setBusinessId(businessId);
            consumption.setBusinessDesc(businessDesc);
            consumption.setStatus(PointsConsumption.Status.SUCCESS);

            int insertResult = pointsConsumptionMapper.insert(consumption);
            
            if (insertResult <= 0) {
                log.error("【插入消费记录失败】用户ID: {}", userId);
                throw new RuntimeException("插入消费记录失败");
            }

            log.info("【消费积分成功】用户ID: {}, 消费前: {}, 消费后: {}, 记录ID: {}", 
                    userId, pointsBefore, pointsAfter, consumption.getId());
            
            return true;

        } catch (Exception e) {
            log.error("【消费积分失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean refundPoints(Long userId, Long pointsAmount, Long originalConsumptionId, String reason) {
        log.info("【退还积分】用户ID: {}, 退还积分: {}, 原消费记录ID: {}, 原因: {}", 
                userId, pointsAmount, originalConsumptionId, reason);

        try {
            // 获取用户当前积分
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.error("【用户不存在】用户ID: {}", userId);
                return false;
            }

            Long pointsBefore = user.getPoints() != null ? user.getPoints() : 0L;
            Long pointsAfter = pointsBefore + pointsAmount;

            // 增加积分
            user.setPoints(pointsAfter);
            int updateResult = userMapper.updateById(user);
            
            if (updateResult <= 0) {
                log.error("【更新用户积分失败】用户ID: {}", userId);
                return false;
            }

            // 记录退款记录
            PointsConsumption refund = new PointsConsumption();
            refund.setUserId(userId);
            refund.setConsumptionType(PointsConsumption.ConsumptionType.OTHER);
            refund.setPointsAmount(-pointsAmount); // 负数表示退款
            refund.setPointsBefore(pointsBefore);
            refund.setPointsAfter(pointsAfter);
            refund.setBusinessId(originalConsumptionId.toString());
            refund.setBusinessDesc("积分退款：" + reason);
            refund.setStatus(PointsConsumption.Status.REFUNDED);

            int insertResult = pointsConsumptionMapper.insert(refund);
            
            if (insertResult <= 0) {
                log.error("【插入退款记录失败】用户ID: {}", userId);
                throw new RuntimeException("插入退款记录失败");
            }

            log.info("【退还积分成功】用户ID: {}, 退还前: {}, 退还后: {}, 记录ID: {}", 
                    userId, pointsBefore, pointsAfter, refund.getId());
            
            return true;

        } catch (Exception e) {
            log.error("【退还积分失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long getUserPoints(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return 0L;
        }
        return user.getPoints() != null ? user.getPoints() : 0L;
    }

    @Override
    @Transactional
    public boolean addUserPoints(Long userId, Long pointsAmount) {
        log.info("【增加用户积分】用户ID: {}, 增加积分: {}", userId, pointsAmount);

        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.error("【用户不存在】用户ID: {}", userId);
                return false;
            }

            Long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
            user.setPoints(currentPoints + pointsAmount);
            
            int result = userMapper.updateById(user);
            boolean success = result > 0;
            
            log.info("【增加用户积分{}】用户ID: {}, 原积分: {}, 新积分: {}", 
                    success ? "成功" : "失败", userId, currentPoints, user.getPoints());
            
            return success;

        } catch (Exception e) {
            log.error("【增加用户积分失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deductUserPoints(Long userId, Long pointsAmount) {
        log.info("【扣除用户积分】用户ID: {}, 扣除积分: {}", userId, pointsAmount);

        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.error("【用户不存在】用户ID: {}", userId);
                return false;
            }

            Long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
            if (currentPoints < pointsAmount) {
                log.warn("【积分不足】用户ID: {}, 当前积分: {}, 需要扣除: {}", userId, currentPoints, pointsAmount);
                return false;
            }

            user.setPoints(currentPoints - pointsAmount);
            
            int result = userMapper.updateById(user);
            boolean success = result > 0;
            
            log.info("【扣除用户积分{}】用户ID: {}, 原积分: {}, 新积分: {}", 
                    success ? "成功" : "失败", userId, currentPoints, user.getPoints());
            
            return success;

        } catch (Exception e) {
            log.error("【扣除用户积分失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long getConsumptionPoints(Integer consumptionType) {
        // 这里可以从配置文件或数据库中获取不同类型的消费积分配置
        // 暂时使用硬编码的方式
        switch (consumptionType) {
            case 1: // 面试功能
                return 10L;
            case 2: // 语音识别 - 按时长计算，1秒=1积分，这里返回基础单位
                return 1L;
            case 3: // AI答复
                return 3L;
            default:
                return 1L;
        }
    }
}
