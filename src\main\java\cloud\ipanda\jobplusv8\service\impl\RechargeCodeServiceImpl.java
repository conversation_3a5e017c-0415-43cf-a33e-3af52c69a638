package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.RechargeCode;
import cloud.ipanda.jobplusv8.mapper.RechargeCodeMapper;
import cloud.ipanda.jobplusv8.service.RechargeCodeService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * 充值码服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class RechargeCodeServiceImpl extends ServiceImpl<RechargeCodeMapper, RechargeCode> implements RechargeCodeService {

    // 加密密钥（实际项目中应该从配置文件读取）
    private static final String ENCRYPT_KEY = "JobPlusV8RechargeCodeKey2025";
    private static final String ALGORITHM = "AES";

    @Override
    @Transactional
    public RechargeCode generateRechargeCode(Long pointsAmount, BigDecimal amount, 
                                           LocalDateTime expireTime, Long creatorId, 
                                           String creatorUsername, String remark) {
        log.info("【生成充值码】积分: {}, 金额: {}, 创建者: {}", pointsAmount, amount, creatorUsername);

        try {
            // 生成原始充值码
            String originalCode = generateOriginalCode();
            
            // 加密充值码
            String encryptedCode = encryptCode(originalCode);
            
            // 生成批次号
            String batchNo = "BATCH_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

            // 创建充值码对象
            RechargeCode rechargeCode = new RechargeCode();
            rechargeCode.setCode(encryptedCode);
            rechargeCode.setOriginalCode(originalCode); // 临时设置，用于返回给调用者
            rechargeCode.setPointsAmount(pointsAmount);
            rechargeCode.setAmount(amount);
            rechargeCode.setStatus(RechargeCode.Status.UNUSED);
            rechargeCode.setExpireTime(expireTime);
            rechargeCode.setBatchNo(batchNo);
            rechargeCode.setCreatorId(creatorId);
            rechargeCode.setCreatorUsername(creatorUsername);
            rechargeCode.setRemark(remark);

            // 保存到数据库
            save(rechargeCode);

            log.info("【生成充值码成功】ID: {}, 原始码: {}, 加密码: {}", 
                    rechargeCode.getId(), originalCode, encryptedCode);

            return rechargeCode;

        } catch (Exception e) {
            log.error("【生成充值码失败】错误: {}", e.getMessage(), e);
            throw new RuntimeException("生成充值码失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public List<RechargeCode> batchGenerateRechargeCodes(Integer count, Long pointsAmount, 
                                                        BigDecimal amount, LocalDateTime expireTime, 
                                                        Long creatorId, String creatorUsername, String remark) {
        log.info("【批量生成充值码】数量: {}, 积分: {}, 创建者: {}", count, pointsAmount, creatorUsername);

        List<RechargeCode> rechargeCodes = new ArrayList<>();
        String batchNo = "BATCH_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        try {
            for (int i = 0; i < count; i++) {
                // 生成原始充值码
                String originalCode = generateOriginalCode();
                
                // 加密充值码
                String encryptedCode = encryptCode(originalCode);

                // 创建充值码对象
                RechargeCode rechargeCode = new RechargeCode();
                rechargeCode.setCode(encryptedCode);
                rechargeCode.setOriginalCode(originalCode); // 临时设置
                rechargeCode.setPointsAmount(pointsAmount);
                rechargeCode.setAmount(amount);
                rechargeCode.setStatus(RechargeCode.Status.UNUSED);
                rechargeCode.setExpireTime(expireTime);
                rechargeCode.setBatchNo(batchNo);
                rechargeCode.setCreatorId(creatorId);
                rechargeCode.setCreatorUsername(creatorUsername);
                rechargeCode.setRemark(remark);

                rechargeCodes.add(rechargeCode);
            }

            // 批量保存
            saveBatch(rechargeCodes);

            log.info("【批量生成充值码成功】数量: {}, 批次号: {}", count, batchNo);
            return rechargeCodes;

        } catch (Exception e) {
            log.error("【批量生成充值码失败】错误: {}", e.getMessage(), e);
            throw new RuntimeException("批量生成充值码失败: " + e.getMessage());
        }
    }

    @Override
    public RechargeCode validateRechargeCode(String code) {
        log.info("【验证充值码】充值码: {}", code);

        try {
            // 先更新过期状态
            updateExpiredCodes();

            // 查询充值码
            QueryWrapper<RechargeCode> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", code)
                       .eq("status", RechargeCode.Status.UNUSED);

            RechargeCode rechargeCode = getOne(queryWrapper);
            
            if (rechargeCode == null) {
                log.warn("【充值码无效】充值码: {}", code);
                return null;
            }

            // 检查是否过期
            if (rechargeCode.getExpireTime() != null && 
                rechargeCode.getExpireTime().isBefore(LocalDateTime.now())) {
                log.warn("【充值码已过期】充值码: {}, 过期时间: {}", code, rechargeCode.getExpireTime());
                return null;
            }

            log.info("【充值码验证成功】ID: {}, 积分: {}", rechargeCode.getId(), rechargeCode.getPointsAmount());
            return rechargeCode;

        } catch (Exception e) {
            log.error("【验证充值码失败】充值码: {}, 错误: {}", code, e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean useRechargeCode(String code, Long userId) {
        log.info("【使用充值码】充值码: {}, 用户ID: {}", code, userId);

        try {
            // 使用数据库层面的原子操作
            int result = baseMapper.useRechargeCode(code, userId);
            
            boolean success = result > 0;
            log.info("【使用充值码{}】充值码: {}, 用户ID: {}", success ? "成功" : "失败", code, userId);
            
            return success;

        } catch (Exception e) {
            log.error("【使用充值码失败】充值码: {}, 用户ID: {}, 错误: {}", code, userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String encryptCode(String originalCode) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = cipher.doFinal(originalCode.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
            
        } catch (Exception e) {
            log.error("【加密充值码失败】原始码: {}, 错误: {}", originalCode, e.getMessage(), e);
            throw new RuntimeException("加密充值码失败");
        }
    }

    @Override
    public String decryptCode(String encryptedCode) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedCode);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("【解密充值码失败】加密码: {}, 错误: {}", encryptedCode, e.getMessage(), e);
            throw new RuntimeException("解密充值码失败");
        }
    }

    @Override
    public String generateOriginalCode() {
        // 生成格式：RC + 年月日 + 8位随机数
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String randomStr = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        return "RC" + dateStr + randomStr;
    }

    @Override
    public int updateExpiredCodes() {
        try {
            int count = baseMapper.updateExpiredCodes();
            if (count > 0) {
                log.info("【更新过期充值码】数量: {}", count);
            }
            return count;
        } catch (Exception e) {
            log.error("【更新过期充值码失败】错误: {}", e.getMessage(), e);
            return 0;
        }
    }
}
