package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.dto.ResumeResponse;
import cloud.ipanda.jobplusv8.dto.ResumeUpdateRequest;
import cloud.ipanda.jobplusv8.dto.ResumeUploadRequest;
import cloud.ipanda.jobplusv8.entity.Resume;
import cloud.ipanda.jobplusv8.mapper.ResumeMapper;
import cloud.ipanda.jobplusv8.service.FileUploadService;
import cloud.ipanda.jobplusv8.service.GeminiService;
import cloud.ipanda.jobplusv8.service.ResumeService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 简历服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class ResumeServiceImpl extends ServiceImpl<ResumeMapper, Resume> implements ResumeService {

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private GeminiService geminiService;

    @Override
    @Transactional
    public ResumeResponse uploadResume(ResumeUploadRequest request, Long userId) {
        log.info("【上传简历】用户ID: {}, 简历名称: {}", userId, request.getResumeName());

        try {
            MultipartFile file = request.getFile();
            
            // 上传文件
            String filePath = fileUploadService.uploadResumeFile(file, userId);
            
            // 如果设为默认简历，先清除其他默认设置
            if (Boolean.TRUE.equals(request.getIsDefault())) {
                baseMapper.clearDefaultResume(userId);
            }

            // 创建简历记录
            Resume resume = new Resume();
            resume.setUserId(userId);
            resume.setResumeName(request.getResumeName());
            resume.setOriginalFilename(file.getOriginalFilename());
            resume.setStoredFilename(new File(filePath).getName());
            resume.setFilePath(filePath);
            resume.setFileSize(file.getSize());
            resume.setFileType(fileUploadService.getFileExtension(file.getOriginalFilename()));
            resume.setMimeType(fileUploadService.getMimeType(file));
            resume.setIsDefault(Boolean.TRUE.equals(request.getIsDefault()) ? 1 : 0);
            resume.setParseStatus(Resume.ParseStatus.NOT_PARSED);
            resume.setStatus(Resume.Status.ENABLED);

            // 保存到数据库
            save(resume);

            // 异步解析简历
            parseResumeAsync(resume.getId());

            log.info("【上传简历成功】简历ID: {}, 文件路径: {}", resume.getId(), filePath);
            return convertToResponse(resume);

        } catch (Exception e) {
            log.error("【上传简历失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("上传简历失败: " + e.getMessage());
        }
    }

    @Override
    public Page<ResumeResponse> getUserResumes(Long userId, Integer current, Integer size) {
        log.info("【获取用户简历列表】用户ID: {}, 页码: {}, 大小: {}", userId, current, size);

        Page<Resume> page = new Page<>(current, size);
        QueryWrapper<Resume> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("is_default")
                   .orderByDesc("create_time");

        Page<Resume> resumePage = page(page, queryWrapper);
        
        Page<ResumeResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(resumePage, responsePage);
        responsePage.setRecords(
            resumePage.getRecords().stream()
                     .map(this::convertToResponse)
                     .collect(Collectors.toList())
        );

        return responsePage;
    }

    @Override
    public List<ResumeResponse> getAllUserResumes(Long userId) {
        log.info("【获取用户所有简历】用户ID: {}", userId);

        QueryWrapper<Resume> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("is_default")
                   .orderByDesc("create_time");

        List<Resume> resumes = list(queryWrapper);
        return resumes.stream()
                     .map(this::convertToResponse)
                     .collect(Collectors.toList());
    }

    @Override
    public ResumeResponse getResumeById(Long id, Long userId) {
        log.info("【获取简历详情】简历ID: {}, 用户ID: {}", id, userId);

        QueryWrapper<Resume> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("user_id", userId);

        Resume resume = getOne(queryWrapper);
        if (resume == null) {
            throw new RuntimeException("简历不存在或无权限访问");
        }

        return convertToResponse(resume);
    }

    @Override
    @Transactional
    public ResumeResponse updateResume(Long id, ResumeUpdateRequest request, Long userId) {
        log.info("【更新简历】简历ID: {}, 用户ID: {}, 新名称: {}", id, userId, request.getResumeName());

        Resume resume = getById(id);
        if (resume == null || !resume.getUserId().equals(userId)) {
            throw new RuntimeException("简历不存在或无权限访问");
        }

        // 如果设为默认简历，先清除其他默认设置
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            baseMapper.clearDefaultResume(userId);
        }

        // 更新简历信息
        resume.setResumeName(request.getResumeName());
        if (request.getIsDefault() != null) {
            resume.setIsDefault(request.getIsDefault() ? 1 : 0);
        }

        updateById(resume);

        log.info("【更新简历成功】简历ID: {}", id);
        return convertToResponse(resume);
    }

    @Override
    @Transactional
    public boolean deleteResume(Long id, Long userId) {
        log.info("【删除简历】简历ID: {}, 用户ID: {}", id, userId);

        Resume resume = getById(id);
        if (resume == null || !resume.getUserId().equals(userId)) {
            throw new RuntimeException("简历不存在或无权限访问");
        }

        // 删除文件
        try {
            fileUploadService.deleteFile(resume.getFilePath());
        } catch (Exception e) {
            log.warn("【删除简历文件失败】文件路径: {}, 错误: {}", resume.getFilePath(), e.getMessage());
        }

        // 删除数据库记录
        boolean deleted = removeById(id);

        log.info("【删除简历{}】简历ID: {}", deleted ? "成功" : "失败", id);
        return deleted;
    }

    @Override
    @Transactional
    public boolean setDefaultResume(Long id, Long userId) {
        log.info("【设置默认简历】简历ID: {}, 用户ID: {}", id, userId);

        Resume resume = getById(id);
        if (resume == null || !resume.getUserId().equals(userId)) {
            throw new RuntimeException("简历不存在或无权限访问");
        }

        // 清除其他默认设置
        baseMapper.clearDefaultResume(userId);

        // 设置当前简历为默认
        resume.setIsDefault(Resume.DefaultFlag.IS_DEFAULT);
        boolean updated = updateById(resume);

        log.info("【设置默认简历{}】简历ID: {}", updated ? "成功" : "失败", id);
        return updated;
    }

    @Override
    public ResumeResponse getDefaultResume(Long userId) {
        log.info("【获取默认简历】用户ID: {}", userId);

        QueryWrapper<Resume> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("is_default", Resume.DefaultFlag.IS_DEFAULT)
                   .orderByDesc("create_time")
                   .last("LIMIT 1");

        Resume resume = getOne(queryWrapper);
        return resume != null ? convertToResponse(resume) : null;
    }

    @Override
    public void downloadResume(Long id, Long userId, HttpServletResponse response) {
        log.info("【下载简历】简历ID: {}, 用户ID: {}", id, userId);

        Resume resume = getById(id);
        if (resume == null || !resume.getUserId().equals(userId)) {
            throw new RuntimeException("简历不存在或无权限访问");
        }

        File file = new File(resume.getFilePath());
        if (!file.exists()) {
            throw new RuntimeException("简历文件不存在");
        }

        try {
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(resume.getOriginalFilename(), "UTF-8"));
            response.setContentLengthLong(file.length());

            // 输出文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            log.info("【下载简历成功】简历ID: {}, 文件: {}", id, resume.getOriginalFilename());

        } catch (IOException e) {
            log.error("【下载简历失败】简历ID: {}, 错误: {}", id, e.getMessage(), e);
            throw new RuntimeException("下载简历失败: " + e.getMessage());
        }
    }

    @Override
    public boolean reparseResume(Long id, Long userId) {
        log.info("【重新解析简历】简历ID: {}, 用户ID: {}", id, userId);

        Resume resume = getById(id);
        if (resume == null || !resume.getUserId().equals(userId)) {
            throw new RuntimeException("简历不存在或无权限访问");
        }

        // 重置解析状态
        resume.setParseStatus(Resume.ParseStatus.NOT_PARSED);
        resume.setParsedContent(null);
        resume.setParseError(null);
        resume.setParsedAt(null);
        updateById(resume);

        // 异步重新解析
        parseResumeAsync(id);

        return true;
    }

    @Override
    @Async
    public void parseResumeAsync(Long resumeId) {
        log.info("【异步解析简历】简历ID: {}", resumeId);

        Resume resume = getById(resumeId);
        if (resume == null) {
            log.error("【异步解析简历失败】简历不存在，ID: {}", resumeId);
            return;
        }

        try {
            // 更新解析状态为解析中
            resume.setParseStatus(Resume.ParseStatus.PARSING);
            updateById(resume);

            // 调用Gemini解析 - 使用新的Base64方式
            File file = new File(resume.getFilePath());
            if (!file.exists()) {
                throw new RuntimeException("简历文件不存在: " + resume.getFilePath());
            }

            log.info("【开始解析简历】文件: {}, 大小: {}KB", file.getName(), file.length() / 1024);

            String parsedContent = geminiService.parseResume(file);

            if (parsedContent != null && !parsedContent.trim().isEmpty()) {
                // 解析成功
                resume.setParseStatus(Resume.ParseStatus.PARSED);
                resume.setParsedContent(parsedContent);
                resume.setParseError(null);
                log.info("【简历解析成功】简历ID: {}, 解析结果长度: {}", resumeId, parsedContent.length());
            } else {
                // 解析失败
                resume.setParseStatus(Resume.ParseStatus.PARSE_FAILED);
                resume.setParseError("解析结果为空");
                log.warn("【简历解析失败】简历ID: {}, 原因: 解析结果为空", resumeId);
            }

            resume.setParsedAt(LocalDateTime.now());
            updateById(resume);

            log.info("【异步解析简历完成】简历ID: {}, 状态: {}", resumeId, resume.getParseStatus());

        } catch (Exception e) {
            log.error("【异步解析简历失败】简历ID: {}, 错误: {}", resumeId, e.getMessage(), e);

            // 更新解析状态为失败
            resume.setParseStatus(Resume.ParseStatus.PARSE_FAILED);
            resume.setParseError(e.getMessage());
            resume.setParsedAt(LocalDateTime.now());
            updateById(resume);
        }
    }

    @Override
    public ResumeResponse convertToResponse(Resume resume) {
        ResumeResponse response = new ResumeResponse();
        BeanUtils.copyProperties(resume, response);
        
        response.setIsDefault(resume.getIsDefault() == 1);
        response.formatFileSize();
        response.setParseStatusDesc();
        
        // 解析JSON内容
        if (resume.getParsedContent() != null && !resume.getParsedContent().trim().isEmpty()) {
            try {
                // 这里可以根据需要解析JSON内容
                response.setParsedContent(resume.getParsedContent());
            } catch (Exception e) {
                log.warn("【解析JSON内容失败】简历ID: {}", resume.getId());
                response.setParsedContent(resume.getParsedContent());
            }
        }

        return response;
    }
}
