package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.SpeechSession;
import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.mapper.SpeechSessionMapper;
import cloud.ipanda.jobplusv8.service.SpeechSessionService;
import cloud.ipanda.jobplusv8.service.UserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 语音识别会话服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Service
public class SpeechSessionServiceImpl extends ServiceImpl<SpeechSessionMapper, SpeechSession> implements SpeechSessionService {

    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public SpeechSession createSession(User user, String websocketSessionId, String clientIp, String userAgent) {
        log.info("【创建语音会话】用户: {}, WebSocket会话: {}", user.getUsername(), websocketSessionId);
        
        // 检查用户是否有足够的时长
        if (!hasEnoughDuration(user.getId())) {
            log.warn("【创建语音会话失败】用户 {} 时长不足", user.getUsername());
            return null;
        }
        
        // 创建会话
        SpeechSession session = new SpeechSession();
        session.setSessionId(UUID.randomUUID().toString().replace("-", ""));
        session.setUserId(user.getId());
        session.setUsername(user.getUsername());
        session.setWebsocketSessionId(websocketSessionId);
        session.setStatus(SpeechSession.Status.ACTIVE);
        session.setStartTime(LocalDateTime.now());
        session.setClientIp(clientIp);
        session.setUserAgent(userAgent);
        session.setRecognitionCount(0);
        session.setTotalWords(0);
        
        if (save(session)) {
            log.info("【创建语音会话成功】会话ID: {}", session.getSessionId());
            return session;
        } else {
            log.error("【创建语音会话失败】数据库保存失败");
            return null;
        }
    }

    @Override
    public boolean hasEnoughDuration(Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return false;
        }

        // 现在基于积分检查，1秒消耗1积分
        long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;

        log.debug("【积分检查】用户: {}, 当前积分: {}, 可用时长: {}s",
                user.getUsername(), currentPoints, currentPoints);

        return currentPoints > 0;
    }

    @Override
    public long getRemainingDuration(Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return 0L;
        }

        // 现在基于积分计算剩余时长，1积分=1秒
        long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;

        return currentPoints; // 积分数量就是可用秒数
    }

    @Override
    @Transactional
    public boolean endSession(String sessionId, String endReason, String remarks) {
        log.info("【结束语音会话】会话ID: {}, 原因: {}", sessionId, endReason);
        
        SpeechSession session = getBySessionId(sessionId);
        if (session == null) {
            log.warn("【结束语音会话失败】会话不存在: {}", sessionId);
            return false;
        }
        
        if (session.getStatus() != SpeechSession.Status.ACTIVE) {
            log.warn("【结束语音会话失败】会话已结束: {}", sessionId);
            return false;
        }
        
        // 计算会话时长
        LocalDateTime endTime = LocalDateTime.now();
        long duration = java.time.Duration.between(session.getStartTime(), endTime).getSeconds();
        
        // 更新会话状态
        LambdaUpdateWrapper<SpeechSession> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SpeechSession::getSessionId, sessionId)
                .set(SpeechSession::getStatus, getStatusByEndReason(endReason))
                .set(SpeechSession::getEndTime, endTime)
                .set(SpeechSession::getDuration, duration)
                .set(SpeechSession::getEndReason, endReason)
                .set(SpeechSession::getRemarks, remarks);
        
        boolean updated = update(updateWrapper);
        
        if (updated && duration > 0) {
            // 扣减用户时长
            deductUserDuration(session.getUserId(), duration);
        }
        
        log.info("【结束语音会话{}】会话ID: {}, 时长: {}s", updated ? "成功" : "失败", sessionId, duration);
        return updated;
    }

    @Override
    public boolean updateSessionDuration(String sessionId, long usedDuration) {
        LambdaUpdateWrapper<SpeechSession> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SpeechSession::getSessionId, sessionId)
                .set(SpeechSession::getDuration, usedDuration);
        
        return update(updateWrapper);
    }

    @Override
    public boolean saveAudioFile(String sessionId, String audioFilePath, long fileSize) {
        log.info("【保存录音文件】会话ID: {}, 文件路径: {}, 大小: {}bytes", sessionId, audioFilePath, fileSize);
        
        LambdaUpdateWrapper<SpeechSession> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SpeechSession::getSessionId, sessionId)
                .set(SpeechSession::getAudioFilePath, audioFilePath)
                .set(SpeechSession::getAudioFileSize, fileSize);
        
        return update(updateWrapper);
    }

    @Override
    public SpeechSession getBySessionId(String sessionId) {
        LambdaQueryWrapper<SpeechSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechSession::getSessionId, sessionId);
        return getOne(queryWrapper);
    }

    @Override
    public SpeechSession getByWebsocketSessionId(String websocketSessionId) {
        LambdaQueryWrapper<SpeechSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechSession::getWebsocketSessionId, websocketSessionId);
        return getOne(queryWrapper);
    }

    @Override
    @Transactional
    public int handleTimeoutSessions() {
        // 查找超过1小时未结束的活跃会话
        LocalDateTime timeoutTime = LocalDateTime.now().minusHours(1);
        
        LambdaQueryWrapper<SpeechSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechSession::getStatus, SpeechSession.Status.ACTIVE)
                .lt(SpeechSession::getStartTime, timeoutTime);
        
        List<SpeechSession> timeoutSessions = list(queryWrapper);
        
        int count = 0;
        for (SpeechSession session : timeoutSessions) {
            if (endSession(session.getSessionId(), SpeechSession.EndReason.TIMEOUT, "系统自动超时结束")) {
                count++;
            }
        }
        
        if (count > 0) {
            log.info("【处理超时会话】处理了 {} 个超时会话", count);
        }
        
        return count;
    }

    @Override
    @Transactional
    public int forceEndUserSessions(Long userId, String reason) {
        log.info("【强制结束用户会话】用户ID: {}, 原因: {}", userId, reason);
        
        LambdaQueryWrapper<SpeechSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechSession::getUserId, userId)
                .eq(SpeechSession::getStatus, SpeechSession.Status.ACTIVE);
        
        List<SpeechSession> activeSessions = list(queryWrapper);
        
        int count = 0;
        for (SpeechSession session : activeSessions) {
            if (endSession(session.getSessionId(), SpeechSession.EndReason.FORCE, reason)) {
                count++;
            }
        }
        
        log.info("【强制结束用户会话完成】用户ID: {}, 结束会话数: {}", userId, count);
        return count;
    }

    @Override
    @Transactional
    public boolean addUserDuration(Long userId, long additionalDuration) {
        log.info("【增加用户积分】用户ID: {}, 增加时长: {}s, 增加积分: {}", userId, additionalDuration, additionalDuration);

        User user = userService.getById(userId);
        if (user == null) {
            log.warn("【增加用户积分失败】用户不存在: {}", userId);
            return false;
        }

        // 基于积分增加，1秒=1积分
        long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
        user.setPoints(currentPoints + additionalDuration);

        boolean updated = userService.updateById(user);
        log.info("【增加用户积分{}】用户ID: {}, 新积分余额: {}", updated ? "成功" : "失败", userId, user.getPoints());

        return updated;
    }

    @Override
    @Transactional
    public boolean deductUserDuration(Long userId, long usedDuration) {
        log.info("【扣减用户积分】用户ID: {}, 使用时长: {}s, 扣减积分: {}", userId, usedDuration, usedDuration);

        User user = userService.getById(userId);
        if (user == null) {
            log.warn("【扣减用户积分失败】用户不存在: {}", userId);
            return false;
        }

        // 基于积分扣减，1秒=1积分
        long currentPoints = user.getPoints() != null ? user.getPoints() : 0L;
        if (currentPoints < usedDuration) {
            log.warn("【扣减用户积分失败】积分不足，用户ID: {}, 当前积分: {}, 需要积分: {}", userId, currentPoints, usedDuration);
            return false;
        }

        user.setPoints(currentPoints - usedDuration);

        boolean updated = userService.updateById(user);
        log.info("【扣减用户积分{}】用户ID: {}, 剩余积分: {}", updated ? "成功" : "失败", userId, user.getPoints());

        return updated;
    }

    /**
     * 根据结束原因获取会话状态
     */
    private int getStatusByEndReason(String endReason) {
        switch (endReason) {
            case SpeechSession.EndReason.TIMEOUT:
                return SpeechSession.Status.TIMEOUT;
            case SpeechSession.EndReason.FORCE:
                return SpeechSession.Status.FORCED;
            default:
                return SpeechSession.Status.ENDED;
        }
    }
}
