package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.dto.*;
import cloud.ipanda.jobplusv8.entity.PointsConsumption;
import cloud.ipanda.jobplusv8.entity.PointsRecharge;
import cloud.ipanda.jobplusv8.entity.RechargeCode;
import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.mapper.PointsConsumptionMapper;
import cloud.ipanda.jobplusv8.mapper.PointsRechargeMapper;
import cloud.ipanda.jobplusv8.mapper.UserMapper;
import cloud.ipanda.jobplusv8.service.PointsService;
import cloud.ipanda.jobplusv8.service.RechargeCodeService;
import cloud.ipanda.jobplusv8.service.UserCenterService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * 用户中心服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class UserCenterServiceImpl implements UserCenterService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PointsRechargeMapper pointsRechargeMapper;

    @Autowired
    private PointsConsumptionMapper pointsConsumptionMapper;

    @Autowired
    private PointsService pointsService;

    @Autowired
    private RechargeCodeService rechargeCodeService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public UserCenterResponse getUserCenterInfo(Long userId) {
        log.info("【获取用户中心信息】用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        UserCenterResponse response = new UserCenterResponse();
        BeanUtils.copyProperties(user, response);

        // 设置状态描述
        response.setStatusDesc();

        log.info("【获取用户中心信息成功】用户ID: {}, 积分: {}", userId, response.getPoints());
        return response;
    }

    @Override
    @Transactional
    public boolean changePassword(Long userId, ChangePasswordRequest request) {
        log.info("【修改密码】用户ID: {}", userId);

        try {
            // 验证确认密码
            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                throw new RuntimeException("新密码与确认密码不一致");
            }

            // 获取用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 验证当前密码
            if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
                throw new RuntimeException("当前密码错误");
            }

            // 更新密码
            user.setPassword(passwordEncoder.encode(request.getNewPassword()));
            int result = userMapper.updateById(user);

            boolean success = result > 0;
            log.info("【修改密码{}】用户ID: {}", success ? "成功" : "失败", userId);
            
            return success;

        } catch (Exception e) {
            log.error("【修改密码失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Page<PointsRechargeResponse> getRechargeRecords(Long userId, Integer current, Integer size) {
        log.info("【获取充值记录】用户ID: {}, 页码: {}, 大小: {}", userId, current, size);

        Page<PointsRecharge> page = new Page<>(current, size);
        QueryWrapper<PointsRecharge> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("create_time");

        Page<PointsRecharge> rechargePage = pointsRechargeMapper.selectPage(page, queryWrapper);
        
        Page<PointsRechargeResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(rechargePage, responsePage);
        responsePage.setRecords(
            rechargePage.getRecords().stream()
                       .map(this::convertToRechargeResponse)
                       .collect(Collectors.toList())
        );

        return responsePage;
    }

    @Override
    public Page<PointsConsumptionResponse> getConsumptionRecords(Long userId, Integer current, Integer size) {
        log.info("【获取消费记录】用户ID: {}, 页码: {}, 大小: {}", userId, current, size);

        Page<PointsConsumption> page = new Page<>(current, size);
        QueryWrapper<PointsConsumption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("create_time");

        Page<PointsConsumption> consumptionPage = pointsConsumptionMapper.selectPage(page, queryWrapper);
        
        Page<PointsConsumptionResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(consumptionPage, responsePage);
        responsePage.setRecords(
            consumptionPage.getRecords().stream()
                          .map(this::convertToConsumptionResponse)
                          .collect(Collectors.toList())
        );

        return responsePage;
    }

    @Override
    @Transactional
    public PointsRechargeResponse rechargeByCode(Long userId, RechargeRequest request) {
        log.info("【充值码充值】用户ID: {}, 充值码: {}", userId, request.getRechargeCode());

        try {
            // 验证充值码
            RechargeCode rechargeCode = rechargeCodeService.validateRechargeCode(request.getRechargeCode());
            if (rechargeCode == null) {
                throw new RuntimeException("充值码无效或已过期");
            }

            // 获取用户当前积分
            Long pointsBefore = pointsService.getUserPoints(userId);

            // 使用充值码
            boolean useSuccess = rechargeCodeService.useRechargeCode(request.getRechargeCode(), userId);
            if (!useSuccess) {
                throw new RuntimeException("充值码使用失败，可能已被使用");
            }

            // 增加用户积分
            boolean addSuccess = pointsService.addUserPoints(userId, rechargeCode.getPointsAmount());
            if (!addSuccess) {
                throw new RuntimeException("增加用户积分失败");
            }

            Long pointsAfter = pointsBefore + rechargeCode.getPointsAmount();

            // 记录充值记录
            PointsRecharge recharge = new PointsRecharge();
            recharge.setUserId(userId);
            recharge.setRechargeType(PointsRecharge.RechargeType.RECHARGE_CODE);
            recharge.setRechargeCode(request.getRechargeCode());
            recharge.setPointsAmount(rechargeCode.getPointsAmount());
            recharge.setPointsBefore(pointsBefore);
            recharge.setPointsAfter(pointsAfter);
            recharge.setAmount(rechargeCode.getAmount());
            recharge.setStatus(PointsRecharge.Status.SUCCESS);
            recharge.setRemark("充值码充值");

            pointsRechargeMapper.insert(recharge);

            log.info("【充值码充值成功】用户ID: {}, 充值积分: {}, 充值前: {}, 充值后: {}", 
                    userId, rechargeCode.getPointsAmount(), pointsBefore, pointsAfter);

            return convertToRechargeResponse(recharge);

        } catch (Exception e) {
            log.error("【充值码充值失败】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    @Transactional
    public PointsRechargeResponse adminRecharge(Long operatorId, String operatorUsername, AdminRechargeRequest request) {
        log.info("【管理员直充】操作员: {}, 目标用户: {}, 充值积分: {}", 
                operatorUsername, request.getUserId(), request.getPointsAmount());

        try {
            // 验证目标用户是否存在
            User targetUser = userMapper.selectById(request.getUserId());
            if (targetUser == null) {
                throw new RuntimeException("目标用户不存在");
            }

            // 获取用户当前积分
            Long pointsBefore = pointsService.getUserPoints(request.getUserId());

            // 增加用户积分
            boolean addSuccess = pointsService.addUserPoints(request.getUserId(), request.getPointsAmount());
            if (!addSuccess) {
                throw new RuntimeException("增加用户积分失败");
            }

            Long pointsAfter = pointsBefore + request.getPointsAmount();

            // 记录充值记录
            PointsRecharge recharge = new PointsRecharge();
            recharge.setUserId(request.getUserId());
            recharge.setRechargeType(PointsRecharge.RechargeType.ADMIN_DIRECT);
            recharge.setPointsAmount(request.getPointsAmount());
            recharge.setPointsBefore(pointsBefore);
            recharge.setPointsAfter(pointsAfter);
            recharge.setOperatorId(operatorId);
            recharge.setOperatorUsername(operatorUsername);
            recharge.setStatus(PointsRecharge.Status.SUCCESS);
            recharge.setRemark(request.getRemark() != null ? request.getRemark() : "管理员直充");

            pointsRechargeMapper.insert(recharge);

            log.info("【管理员直充成功】操作员: {}, 目标用户: {}, 充值积分: {}, 充值前: {}, 充值后: {}", 
                    operatorUsername, request.getUserId(), request.getPointsAmount(), pointsBefore, pointsAfter);

            return convertToRechargeResponse(recharge);

        } catch (Exception e) {
            log.error("【管理员直充失败】操作员: {}, 目标用户: {}, 错误: {}", 
                    operatorUsername, request.getUserId(), e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 转换充值记录为响应DTO
     */
    private PointsRechargeResponse convertToRechargeResponse(PointsRecharge recharge) {
        PointsRechargeResponse response = new PointsRechargeResponse();
        BeanUtils.copyProperties(recharge, response);
        
        // 设置描述信息
        response.setRechargeTypeDesc();
        response.setStatusDesc();
        
        // 设置充值码脱敏显示
        if (recharge.getRechargeCode() != null) {
            response.setRechargeCodeMasked(recharge.getRechargeCode());
        }
        
        return response;
    }

    /**
     * 转换消费记录为响应DTO
     */
    private PointsConsumptionResponse convertToConsumptionResponse(PointsConsumption consumption) {
        PointsConsumptionResponse response = new PointsConsumptionResponse();
        BeanUtils.copyProperties(consumption, response);
        
        // 设置描述信息
        response.setConsumptionTypeDesc();
        response.setStatusDesc();
        
        return response;
    }
}
