package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.mapper.UserMapper;
import cloud.ipanda.jobplusv8.service.UserService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User getByUsername(String username) {
        return baseMapper.selectByUsername(username);
    }

    @Override
    public User getByEmail(String email) {
        return baseMapper.selectByEmail(email);
    }

    @Override
    public boolean register(User user) {
        try {
            // 检查用户名是否已存在
            if (getByUsername(user.getUsername()) != null) {
                log.warn("用户名已存在: {}", user.getUsername());
                return false;
            }
            
            // 检查邮箱是否已存在
            if (getByEmail(user.getEmail()) != null) {
                log.warn("邮箱已存在: {}", user.getEmail());
                return false;
            }
            
            // 密码加密
            String encryptedPassword = passwordEncoder.encode(user.getPassword());
            user.setPassword(encryptedPassword);
            
            // 设置默认状态
            user.setStatus(1);
            
            // 保存用户
            return save(user);
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return false;
        }
    }

    @Override
    public User login(String usernameOrEmail, String password) {
        try {
            // 根据用户名或邮箱查询用户
            User user = null;

            // 判断输入的是邮箱还是用户名
            if (usernameOrEmail.contains("@")) {
                // 包含@符号，按邮箱查询
                user = getByEmail(usernameOrEmail);
                if (user == null) {
                    log.warn("邮箱对应的用户不存在: {}", usernameOrEmail);
                    return null;
                }
            } else {
                // 不包含@符号，按用户名查询
                user = getByUsername(usernameOrEmail);
                if (user == null) {
                    log.warn("用户名不存在: {}", usernameOrEmail);
                    return null;
                }
            }

            // 检查用户状态
            if (user.getStatus() != 1) {
                log.warn("用户已被禁用: {}", usernameOrEmail);
                return null;
            }

            // 验证密码
            if (!passwordEncoder.matches(password, user.getPassword())) {
                log.warn("密码错误: {}", usernameOrEmail);
                return null;
            }

            // 清空密码字段
            user.setPassword(null);
            return user;
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return null;
        }
    }

    @Override
    public boolean existsByUsername(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        return count(queryWrapper) > 0;
    }
}
