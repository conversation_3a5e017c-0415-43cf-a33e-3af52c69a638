package cloud.ipanda.jobplusv8.task;

import cloud.ipanda.jobplusv8.service.AudioFileService;
import cloud.ipanda.jobplusv8.service.SpeechSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 语音识别系统维护定时任务
 * 
 * 功能说明：
 * 1. 定期清理过期的录音文件
 * 2. 处理超时的语音识别会话
 * 3. 系统资源监控和清理
 * 4. 统计信息更新
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Component
@Slf4j
public class SpeechMaintenanceTask {

    @Autowired
    private AudioFileService audioFileService;

    @Autowired
    private SpeechSessionService speechSessionService;

    @Value("${audio.storage.cleanup-days:30}")
    private int cleanupDays;

    /**
     * 清理过期录音文件
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredAudioFiles() {
        log.info("【定时任务】开始清理过期录音文件，保留天数: {}", cleanupDays);
        
        try {
            int deletedCount = audioFileService.cleanupExpiredFiles(cleanupDays);
            log.info("【定时任务】清理过期录音文件完成，删除文件数: {}", deletedCount);
        } catch (Exception e) {
            log.error("【定时任务】清理过期录音文件失败", e);
        }
    }

    /**
     * 处理超时会话
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void handleTimeoutSessions() {
        try {
            int timeoutCount = speechSessionService.handleTimeoutSessions();
            if (timeoutCount > 0) {
                log.info("【定时任务】处理超时会话完成，处理数量: {}", timeoutCount);
            }
        } catch (Exception e) {
            log.error("【定时任务】处理超时会话失败", e);
        }
    }

    /**
     * 系统状态监控
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void systemStatusMonitoring() {
        try {
            // 获取系统运行状态
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
            
            log.info("【系统监控】内存使用情况 - 总内存: {}MB, 已用: {}MB, 可用: {}MB, 使用率: {:.2f}%",
                    totalMemory / 1024 / 1024,
                    usedMemory / 1024 / 1024,
                    freeMemory / 1024 / 1024,
                    memoryUsagePercent);
            
            // 内存使用率过高时发出警告
            if (memoryUsagePercent > 80) {
                log.warn("【系统监控】内存使用率过高: {:.2f}%，建议检查系统状态", memoryUsagePercent);
            }
            
            // 获取录音文件存储目录大小
            String storageDir = audioFileService.getAudioStorageDir();
            log.info("【系统监控】录音文件存储目录: {}", storageDir);
            
        } catch (Exception e) {
            log.error("【定时任务】系统状态监控失败", e);
        }
    }

    /**
     * 数据库连接池监控
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1800000毫秒
    public void databaseConnectionMonitoring() {
        try {
            // TODO: 添加数据库连接池监控逻辑
            // 可以通过HikariCP的MBean获取连接池状态
            log.debug("【系统监控】数据库连接池状态检查");
            
        } catch (Exception e) {
            log.error("【定时任务】数据库连接池监控失败", e);
        }
    }

    /**
     * 清理临时文件
     * 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanupTempFiles() {
        log.info("【定时任务】开始清理临时文件");
        
        try {
            // 清理系统临时目录中的相关文件
            String tempDir = System.getProperty("java.io.tmpdir");
            log.info("【定时任务】系统临时目录: {}", tempDir);
            
            // TODO: 实现临时文件清理逻辑
            // 清理以特定前缀命名的临时文件
            
            log.info("【定时任务】清理临时文件完成");
            
        } catch (Exception e) {
            log.error("【定时任务】清理临时文件失败", e);
        }
    }

    /**
     * 生成每日统计报告
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateDailyReport() {
        log.info("【定时任务】开始生成每日统计报告");
        
        try {
            // TODO: 实现每日统计报告生成
            // 1. 统计昨日语音识别使用情况
            // 2. 统计用户活跃度
            // 3. 统计系统性能指标
            // 4. 生成报告文件或发送邮件
            
            log.info("【定时任务】每日统计报告生成完成");
            
        } catch (Exception e) {
            log.error("【定时任务】生成每日统计报告失败", e);
        }
    }

    /**
     * 健康检查
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 1分钟 = 60000毫秒
    public void healthCheck() {
        try {
            // 检查关键服务是否正常
            boolean audioServiceHealthy = checkAudioServiceHealth();
            boolean sessionServiceHealthy = checkSessionServiceHealth();
            
            if (!audioServiceHealthy || !sessionServiceHealthy) {
                log.warn("【健康检查】系统服务状态异常 - 音频服务: {}, 会话服务: {}", 
                        audioServiceHealthy, sessionServiceHealthy);
            }
            
        } catch (Exception e) {
            log.error("【定时任务】健康检查失败", e);
        }
    }

    /**
     * 检查音频服务健康状态
     */
    private boolean checkAudioServiceHealth() {
        try {
            // 检查音频存储目录是否可访问
            String storageDir = audioFileService.getAudioStorageDir();
            return storageDir != null && !storageDir.isEmpty();
        } catch (Exception e) {
            log.error("【健康检查】音频服务检查失败", e);
            return false;
        }
    }

    /**
     * 检查会话服务健康状态
     */
    private boolean checkSessionServiceHealth() {
        try {
            // 检查会话服务是否正常
            // 可以通过查询数据库或调用服务方法来验证
            return speechSessionService != null;
        } catch (Exception e) {
            log.error("【健康检查】会话服务检查失败", e);
            return false;
        }
    }
}
