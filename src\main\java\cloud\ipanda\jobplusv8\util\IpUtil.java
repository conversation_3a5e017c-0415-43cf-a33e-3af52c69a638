package cloud.ipanda.jobplusv8.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * IP工具类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
public class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }

        String ip = null;
        try {
            // 1. 检查 X-Forwarded-For 头（代理服务器会设置）
            ip = request.getHeader("X-Forwarded-For");
            if (isValidIp(ip)) {
                // 多个IP时取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }

            // 2. 检查 Proxy-Client-IP 头（Apache服务器会设置）
            ip = request.getHeader("Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 3. 检查 WL-Proxy-Client-IP 头（WebLogic服务器会设置）
            ip = request.getHeader("WL-Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 4. 检查 HTTP_CLIENT_IP 头
            ip = request.getHeader("HTTP_CLIENT_IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 5. 检查 HTTP_X_FORWARDED_FOR 头
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            if (isValidIp(ip)) {
                return ip;
            }

            // 6. 检查 X-Real-IP 头（Nginx代理会设置）
            ip = request.getHeader("X-Real-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 7. 最后使用 getRemoteAddr() 方法获取
            ip = request.getRemoteAddr();
            if (LOCALHOST_IPV6.equals(ip)) {
                ip = LOCALHOST_IPV4;
            }

        } catch (Exception e) {
            log.error("获取客户端IP地址失败", e);
        }

        return StringUtils.hasText(ip) ? ip : UNKNOWN;
    }

    /**
     * 验证IP地址是否有效
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && 
               !UNKNOWN.equalsIgnoreCase(ip) && 
               !isInternalIp(ip);
    }

    /**
     * 判断是否为内网IP
     * 
     * @param ip IP地址
     * @return 是否为内网IP
     */
    private static boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return true;
        }

        try {
            String[] parts = ip.split("\\.");
            if (parts.length != 4) {
                return false;
            }

            int firstOctet = Integer.parseInt(parts[0]);
            int secondOctet = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (firstOctet == 10) {
                return true;
            }

            // ********** - **************
            if (firstOctet == 172 && secondOctet >= 16 && secondOctet <= 31) {
                return true;
            }

            // *********** - ***************
            if (firstOctet == 192 && secondOctet == 168) {
                return true;
            }

            // ********* - *************** (本地回环)
            if (firstOctet == 127) {
                return true;
            }

        } catch (NumberFormatException e) {
            return false;
        }

        return false;
    }

    /**
     * 获取客户端操作系统信息
     * 
     * @param userAgent User-Agent字符串
     * @return 操作系统信息
     */
    public static String getOperatingSystem(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return UNKNOWN;
        }

        userAgent = userAgent.toLowerCase();

        if (userAgent.contains("windows")) {
            return "Windows";
        } else if (userAgent.contains("mac")) {
            return "Mac OS";
        } else if (userAgent.contains("linux")) {
            return "Linux";
        } else if (userAgent.contains("android")) {
            return "Android";
        } else if (userAgent.contains("iphone") || userAgent.contains("ipad")) {
            return "iOS";
        } else {
            return UNKNOWN;
        }
    }

    /**
     * 获取客户端浏览器信息
     * 
     * @param userAgent User-Agent字符串
     * @return 浏览器信息
     */
    public static String getBrowser(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return UNKNOWN;
        }

        userAgent = userAgent.toLowerCase();

        if (userAgent.contains("chrome")) {
            return "Chrome";
        } else if (userAgent.contains("firefox")) {
            return "Firefox";
        } else if (userAgent.contains("safari")) {
            return "Safari";
        } else if (userAgent.contains("edge")) {
            return "Edge";
        } else if (userAgent.contains("opera")) {
            return "Opera";
        } else if (userAgent.contains("msie") || userAgent.contains("trident")) {
            return "Internet Explorer";
        } else {
            return UNKNOWN;
        }
    }
}
