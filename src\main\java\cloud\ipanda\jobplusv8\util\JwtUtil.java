package cloud.ipanda.jobplusv8.util;

import cloud.ipanda.jobplusv8.service.JwtRedisService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKey;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Component
public class JwtUtil {

    @Autowired
    private JwtRedisService jwtRedisService;

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:jobplusv8-secret-key-for-jwt-token-generation-and-validation}")
    private String secret;

    /**
     * JWT过期时间（毫秒）
     */
    @Value("${jwt.expiration:86400000}")
    private Long expiration;

    /**
     * 生成JWT Token并存储到Redis
     *
     * @param userId 用户ID
     * @param username 用户名
     * @return JWT Token
     */
    public String generateToken(Long userId, String username) {
        log.info("【JWT生成】开始为用户生成Token, 用户ID: {}, 用户名: {}", userId, username);

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);

        String token = createToken(claims, username);

        // 存储到Redis，过期时间转换为秒
        long expireSeconds = expiration / 1000;
        jwtRedisService.storeToken(userId, token, expireSeconds);

        log.info("【JWT生成】Token生成并存储成功, 用户ID: {}, 过期时间: {}秒", userId, expireSeconds);
        return token;
    }

    /**
     * 创建Token
     * 
     * @param claims 声明
     * @param subject 主题
     * @return JWT Token
     */
    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Token中获取用户名
     * 
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return claims.get("userId", Long.class);
    }

    /**
     * 从Token中获取过期时间
     * 
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * 从Token中获取指定声明
     * 
     * @param token JWT Token
     * @param claimsResolver 声明解析器
     * @param <T> 返回类型
     * @return 声明值
     */
    public <T> T getClaimFromToken(String token, ClaimsResolver<T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.resolve(claims);
    }

    /**
     * 从Token中获取所有声明
     * 
     * @param token JWT Token
     * @return 所有声明
     */
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 检查Token是否过期
     * 
     * @param token JWT Token
     * @return 是否过期
     */
    public Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * 验证Token（包括Redis验证）
     *
     * @param token JWT Token
     * @param username 用户名
     * @return 是否有效
     */
    public Boolean validateToken(String token, String username) {
        try {
            log.debug("【JWT验证】开始验证Token, 用户名: {}", username);

            // 1. 验证Token格式和签名
            final String tokenUsername = getUsernameFromToken(token);
            if (!tokenUsername.equals(username)) {
                log.warn("【JWT验证失败】用户名不匹配, 期望: {}, 实际: {}", username, tokenUsername);
                return false;
            }

            // 2. 验证Token是否过期
            if (isTokenExpired(token)) {
                log.warn("【JWT验证失败】Token已过期, 用户名: {}", username);
                return false;
            }

            // 3. 验证Token是否存在于Redis中
            Long userId = getUserIdFromToken(token);
            if (!jwtRedisService.validateToken(userId, token)) {
                log.warn("【JWT验证失败】Token在Redis中不存在或不匹配, 用户ID: {}", userId);
                return false;
            }

            log.debug("【JWT验证成功】用户名: {}, 用户ID: {}", username, userId);
            return true;

        } catch (Exception e) {
            log.error("【JWT验证异常】用户名: {}, 错误: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从HTTP请求中获取JWT Token
     *
     * @param request HTTP请求
     * @return JWT Token，如果不存在则返回null
     */
    public String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 从HTTP请求中获取当前用户ID（包括Redis验证）
     *
     * @param request HTTP请求
     * @return 用户ID，如果Token无效则返回null
     */
    public Long getCurrentUserIdFromRequest(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token != null) {
                Long userId = getUserIdFromToken(token);
                String username = getUsernameFromToken(token);

                // 验证Token是否有效（包括Redis验证）
                if (validateToken(token, username)) {
                    return userId;
                } else {
                    log.warn("【JWT验证失败】Token无效, 用户ID: {}", userId);
                }
            }
        } catch (Exception e) {
            log.error("【从请求获取用户ID失败】错误: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 刷新Token过期时间
     *
     * @param userId 用户ID
     * @return 是否刷新成功
     */
    public boolean refreshToken(Long userId) {
        try {
            long expireSeconds = expiration / 1000;
            boolean success = jwtRedisService.refreshTokenExpire(userId, expireSeconds);
            if (success) {
                log.info("【JWT刷新】Token过期时间刷新成功, 用户ID: {}", userId);
            } else {
                log.warn("【JWT刷新】Token过期时间刷新失败, 用户ID: {}", userId);
            }
            return success;
        } catch (Exception e) {
            log.error("【JWT刷新异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 注销用户（删除Redis中的Token）
     *
     * @param userId 用户ID
     */
    public void logout(Long userId) {
        try {
            jwtRedisService.removeToken(userId);
            log.info("【JWT注销】用户Token已删除, 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("【JWT注销异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 检查用户是否在线
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isUserOnline(Long userId) {
        try {
            return jwtRedisService.isUserOnline(userId);
        } catch (Exception e) {
            log.error("【用户在线状态查询异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取Token剩余过期时间
     *
     * @param userId 用户ID
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示不存在
     */
    public long getTokenExpireTime(Long userId) {
        try {
            return jwtRedisService.getTokenExpire(userId);
        } catch (Exception e) {
            log.error("【Token过期时间查询异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return -2;
        }
    }

    /**
     * 获取签名密钥
     * 
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 声明解析器接口
     * 
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface ClaimsResolver<T> {
        T resolve(Claims claims);
    }
}
