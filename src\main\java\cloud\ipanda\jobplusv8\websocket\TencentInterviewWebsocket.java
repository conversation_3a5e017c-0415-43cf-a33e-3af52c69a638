package cloud.ipanda.jobplusv8.websocket;

import cloud.ipanda.jobplusv8.config.TencentSpeechConfig;
import cloud.ipanda.jobplusv8.entity.SpeechSession;
import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.listener.TencentSpeechRecognizerListener;
import cloud.ipanda.jobplusv8.service.AudioFileService;
import cloud.ipanda.jobplusv8.service.SpeechSessionService;
import cloud.ipanda.jobplusv8.service.UserService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencent.asrv2.AsrConstant;
import com.tencent.asrv2.SpeechRecognizer;
import com.tencent.asrv2.SpeechRecognizerRequest;
import com.tencent.core.ws.Credential;
import com.tencent.core.ws.SpeechClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 面试语音识别WebSocket端点
 *
 * 功能说明：
 * 1. 提供WebSocket连接端点，接收客户端的实时音频流
 * 2. 集成腾讯云语音识别服务，实现实时语音转文字
 * 3. 管理语音识别会话的完整生命周期
 * 4. 处理WebSocket连接的建立、消息传输、错误处理和连接关闭
 * 5. 实现JWT权限认证，确保只有合法用户可以使用语音识别
 * 6. 管理用户时长，支持按时间收费模式
 * 7. 录音文件持久化存储，支持后续回放和分析
 *
 * 技术架构：
 * - WebSocket协议：实现客户端与服务器的双向实时通信
 * - 腾讯云ASR：提供高精度的中文语音识别服务
 * - Spring集成：利用Spring的依赖注入管理配置和服务
 * - 异步处理：支持并发的多个语音识别会话
 * - JWT认证：基于令牌的用户身份验证
 * - 时长管理：用户可用时长验证和扣减
 * - 文件存储：录音文件的持久化和管理
 *
 * 数据流程：
 * 1. 客户端建立WebSocket连接 -> onOpen()
 * 2. 客户端发送认证信息 -> 验证JWT令牌和用户时长
 * 3. 认证成功后创建语音识别会话
 * 4. 客户端发送音频数据 -> onMessage()
 * 5. 音频数据同时发送给腾讯云ASR和录音文件
 * 6. ASR服务返回识别结果 -> TencentSpeechRecognizerListener
 * 7. 监控会话时长，超时自动结束
 * 8. 客户端断开连接 -> onClose()
 *
 * 安全考虑：
 * - JWT令牌验证，确保用户身份合法
 * - 用户时长验证，防止超额使用
 * - 音频数据传输使用二进制格式，减少带宽占用
 * - 每个连接独立的识别会话，避免数据混淆
 * - 完善的错误处理和资源清理机制
 * - 录音文件安全存储，按用户隔离
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-23
 */
@Component  // Spring组件注解，但WebSocket端点不会被Spring直接管理
@Slf4j      // Lombok日志注解，自动生成log对象
@ServerEndpoint("/asr/tencent")  // WebSocket服务端点，客户端通过此路径连接
public class TencentInterviewWebsocket {

    /**
     * Spring应用上下文，用于获取Spring管理的Bean
     * 由于WebSocket端点不是Spring管理的Bean，需要手动获取依赖
     */
    private static ApplicationContext applicationContext;

    /**
     * 腾讯云语音识别客户端
     * 使用默认的实时识别请求URL，负责与腾讯云ASR服务建立连接
     */
    private final SpeechClient speechClient = new SpeechClient(AsrConstant.DEFAULT_RT_REQ_URL);

    /**
     * 腾讯云语音识别配置对象
     * 包含API密钥、区域、应用ID等认证和配置信息
     */
    private TencentSpeechConfig tencentSpeechConfig;

    /**
     * 腾讯云API认证凭据
     * 基于配置信息生成，用于API请求的身份验证
     */
    private Credential credential;

    /**
     * 语音识别事件监听器
     * 处理识别过程中的各种回调事件，如识别开始、结果变化、识别完成等
     */
    private TencentSpeechRecognizerListener tencentSpeechRecognizerListener;

    /**
     * 语音识别请求配置对象
     * 包含识别引擎类型、音频格式、VAD设置等参数
     */
    private SpeechRecognizerRequest request = makeRequest();

    /**
     * 语音识别器实例
     * 负责实际的语音识别处理，管理与腾讯云ASR服务的通信
     */
    private SpeechRecognizer speechRecognizer;

    /**
     * JWT工具类
     * 用于验证用户身份令牌
     */
    private JwtUtil jwtUtil;

    /**
     * 用户服务
     * 用于获取用户信息和验证用户状态
     */
    private UserService userService;

    /**
     * 语音识别会话服务
     * 管理语音识别会话的生命周期和用户时长
     */
    private SpeechSessionService speechSessionService;

    /**
     * 录音文件服务
     * 管理录音文件的创建、写入和存储
     */
    private AudioFileService audioFileService;

    /**
     * 当前会话的用户信息
     */
    private User currentUser;

    /**
     * 当前语音识别会话
     */
    private SpeechSession currentSpeechSession;

    /**
     * 当前录音文件路径
     */
    private String currentAudioFilePath;

    /**
     * 会话开始时间（毫秒）
     */
    private long sessionStartTime;

    /**
     * 是否已认证
     */
    private boolean authenticated = false;

    /**
     * WebSocket会话对象
     */
    private Session websocketSession;

    /**
     * 定时任务执行器，用于监控会话时长
     */
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 全局会话管理，用于监控所有活跃会话
     */
    private static final Map<String, TencentInterviewWebsocket> activeSessions = new ConcurrentHashMap<>();


    /**
     * WebSocket连接建立事件处理
     *
     * 功能说明：
     * 1. 当客户端成功建立WebSocket连接时触发
     * 2. 初始化语音识别相关的配置和服务
     * 3. 创建并启动语音识别器实例
     * 4. 准备接收音频数据流
     *
     * 初始化流程：
     * 1. 从Spring容器获取腾讯云配置和监听器Bean
     * 2. 创建API认证凭据
     * 3. 初始化语音识别器
     * 4. 启动识别服务
     *
     * 异常处理：
     * - 配置获取失败
     * - 认证凭据创建失败
     * - 语音识别器启动失败
     *
     * @throws Exception 当初始化过程中发生错误时抛出异常
     */
    @OnOpen
    public void onOpen(Session session) throws Exception {
        this.websocketSession = session;
        String sessionId = session.getId();
        activeSessions.put(sessionId, this);

        log.info("【WebSocket连接建立】会话ID: {}, 远程地址: {}", sessionId, getClientIp(session));

        // 初始化Spring Bean依赖
        initializeDependencies();

        // 发送连接成功消息，要求客户端进行认证
        sendMessage(createMessage("CONNECTION_STATUS", "CONNECTED", "WebSocket连接已建立，请发送认证信息"));

        // 设置30秒认证超时
        scheduler.schedule(() -> {
            if (!authenticated) {
                log.warn("【认证超时】会话ID: {}", sessionId);
                sendMessage(createMessage("AUTH_ERROR", "TIMEOUT", "认证超时，连接将被关闭"));
                closeConnection("认证超时");
            }
        }, 30, TimeUnit.SECONDS);

        log.info("【WebSocket连接建立】等待客户端认证");

        // 从Spring容器获取配置Bean（因为WebSocket端点不受Spring管理）
        tencentSpeechConfig = applicationContext.getBean(TencentSpeechConfig.class);
        tencentSpeechRecognizerListener = applicationContext.getBean(TencentSpeechRecognizerListener.class);

        // 创建腾讯云API认证凭据
        credential = new Credential(
                tencentSpeechConfig.getAppId(),      // 应用ID
                tencentSpeechConfig.getSecretId(),   // API密钥ID
                tencentSpeechConfig.getSecretKey()   // API密钥Key
        );

        // 创建语音识别器实例，传入客户端、凭据、请求配置和事件监听器
        speechRecognizer = new SpeechRecognizer(speechClient, credential, request, tencentSpeechRecognizerListener);

        // 启动语音识别服务，开始监听音频数据
        speechRecognizer.start();

        log.info("【WebSocket连接建立】语音识别服务初始化完成，准备接收音频数据");
    }

    /**
     * WebSocket消息接收事件处理（文本消息）
     *
     * 功能说明：
     * 1. 处理客户端发送的文本消息（如认证信息）
     * 2. 解析JSON格式的消息
     * 3. 根据消息类型执行相应的处理逻辑
     *
     * @param message 客户端发送的文本消息
     * @param session WebSocket会话对象
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            JSONObject data = JSON.parseObject(message);
            String type = data.getString("type");

            log.debug("【接收文本消息】类型: {}, 会话ID: {}", type, session.getId());

            switch (type) {
                case "AUTH":
                    handleAuthentication(data, session);
                    break;
                case "AUDIO_DATA":
                    handleAudioData(data, session);
                    break;
                case "END_SESSION":
                    handleEndSession(data, session);
                    break;
                default:
                    sendMessage(createMessage("ERROR", "UNKNOWN_MESSAGE_TYPE", "未知的消息类型: " + type));
            }
        } catch (Exception e) {
            log.error("【处理文本消息失败】会话ID: {}", session.getId(), e);
            sendMessage(createMessage("ERROR", "MESSAGE_PROCESS_ERROR", "消息处理失败: " + e.getMessage()));
        }
    }

    /**
     * WebSocket消息接收事件处理
     *
     * 功能说明：
     * 1. 接收客户端发送的二进制音频数据
     * 2. 将音频数据转发给腾讯云语音识别服务
     * 3. 进行状态检查确保数据发送的有效性
     *
     * 数据处理流程：
     * 1. 接收ByteBuffer格式的音频数据
     * 2. 检查语音识别器状态是否允许发送数据
     * 3. 将音频数据写入识别器进行处理
     * 4. 识别结果通过监听器异步返回
     *
     * 音频格式要求：
     * - 采样率：16kHz
     * - 编码格式：PCM
     * - 声道：单声道
     * - 位深：16位
     *
     * 性能考虑：
     * - 使用二进制传输减少网络开销
     * - 实时处理，低延迟响应
     * - 状态检查避免无效数据发送
     *
     * @param message 客户端发送的音频数据，ByteBuffer格式
     * @param session WebSocket会话对象，用于连接管理
     * @throws Exception 当数据处理过程中发生错误时抛出异常
     */
    @OnMessage
    public void onMessage(ByteBuffer message, Session session) throws Exception {
        // 检查是否已认证
        if (!authenticated) {
            log.warn("【未认证音频数据】会话ID: {}", session.getId());
            return;
        }

        // 检查用户剩余时长
        long currentDuration = (System.currentTimeMillis() - sessionStartTime) / 1000;
        long remainingDuration = speechSessionService.getRemainingDuration(currentUser.getId());

        if (currentDuration >= remainingDuration) {
            log.info("【用户时长耗尽】用户: {}, 已用时长: {}s", currentUser.getUsername(), currentDuration);
            endSession(SpeechSession.EndReason.NO_DURATION, "用户时长耗尽");
            return;
        }

        // 写入录音文件
        if (currentAudioFilePath != null) {
            try {
                audioFileService.writeAudioData(currentAudioFilePath, message.array());
            } catch (IOException e) {
                log.error("【写入录音文件失败】", e);
            }
        }

        // 将音频数据写入语音识别器进行处理
        speechRecognizer.write(message.array());

        // 注：识别结果会通过TencentSpeechRecognizerListener异步回调返回
    }

    /**
     * WebSocket连接关闭事件处理
     *
     * 功能说明：
     * 1. 当客户端断开WebSocket连接时触发
     * 2. 清理语音识别相关资源
     * 3. 停止语音识别服务
     * 4. 释放网络连接和内存资源
     *
     * 清理流程：
     * 1. 检查语音识别器是否存在
     * 2. 停止语音识别服务
     * 3. 关闭与腾讯云的连接
     * 4. 释放相关资源
     *
     * 资源管理：
     * - 避免内存泄漏
     * - 及时释放网络连接
     * - 清理临时数据
     *
     * @throws Exception 当资源清理过程中发生错误时抛出异常
     */
    @OnClose
    public void onClose() throws Exception {
        String sessionId = websocketSession != null ? websocketSession.getId() : "unknown";
        log.info("【WebSocket连接关闭】会话ID: {}", sessionId);

        // 结束语音识别会话
        if (authenticated && currentSpeechSession != null) {
            endSession(SpeechSession.EndReason.NORMAL, "客户端主动断开连接");
        }

        // 检查语音识别器是否已初始化
        if (speechRecognizer != null) {
            // 停止语音识别服务
            speechRecognizer.stop();
            // 关闭与腾讯云的连接，释放资源
            speechRecognizer.close();

            log.info("【WebSocket连接关闭】语音识别资源清理完成");
        }

        // 清理资源
        cleanup(sessionId);
    }


    /**
     * WebSocket错误处理事件
     *
     * 功能说明：
     * 1. 当WebSocket连接或处理过程中发生错误时触发
     * 2. 记录详细的错误信息用于问题诊断
     * 3. 执行错误恢复和资源清理操作
     * 4. 优雅地关闭连接，避免资源泄漏
     *
     * 错误类型：
     * - 网络连接异常
     * - 音频数据格式错误
     * - 腾讯云服务异常
     * - 系统资源不足
     * - 认证失败
     *
     * 处理策略：
     * 1. 记录错误详情和会话信息
     * 2. 清理语音识别器资源
     * 3. 关闭WebSocket连接
     * 4. 通知客户端错误原因
     *
     * @param session WebSocket会话对象
     * @param error 发生的异常对象
     */
    @OnError
    public void onError(Session session, Throwable error) {
        // 获取会话ID，用于错误追踪
        String sessionId = (session != null) ? session.getId() : "unknown-session";
        log.error("【WebSocket错误】会话ID: {}, 错误信息: {}", sessionId, error.getMessage(), error);

        // 结束语音识别会话
        if (authenticated && currentSpeechSession != null) {
            endSession(SpeechSession.EndReason.ERROR, "连接错误: " + error.getMessage());
        }

        // 清理语音识别器资源
        closeSpeechRecognizer();

        // 关闭WebSocket会话并通知错误原因
        closeSessionWithError(session, "内部错误: " + error.getMessage());

        // 清理资源
        cleanup(sessionId);
    }

    /**
     * 关闭WebSocket会话并发送错误信息
     *
     * @param session WebSocket会话对象
     * @param reason 关闭原因描述
     */
    private void closeSessionWithError(Session session, String reason) {
        if (session != null && session.isOpen()) {
            try {
                // 使用标准的WebSocket关闭代码和原因关闭连接
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, reason));
                log.info("【WebSocket会话关闭】原因: {}", reason);
            } catch (IOException e) {
                log.error("【WebSocket会话关闭失败】", e);
            }
        }
    }

    /**
     * 安全地关闭语音识别器
     *
     * 功能说明：
     * 1. 停止语音识别服务
     * 2. 关闭与腾讯云的连接
     * 3. 释放相关资源
     * 4. 重置识别器引用
     *
     * 异常处理：
     * - 捕获并记录关闭过程中的异常
     * - 确保即使发生异常也能完成资源清理
     */
    private void closeSpeechRecognizer() {
        try {
            if (speechRecognizer != null) {
                // 停止语音识别服务
                speechRecognizer.stop();
                // 关闭连接，释放资源
                speechRecognizer.close();
                // 重置引用，帮助垃圾回收
                speechRecognizer = null;
                log.info("【语音识别器关闭】资源清理成功");
            }
        } catch (Exception e) {
            log.error("【语音识别器关闭失败】", e);
        }
    }


    /**
     * 构造语音识别请求配置对象
     *
     * 功能说明：
     * 1. 初始化语音识别的各项参数
     * 2. 配置适合中文面试场景的识别模型
     * 3. 设置音频格式和处理选项
     * 4. 生成唯一的请求标识符
     *
     * 配置参数说明：
     * - 引擎模型：16k_zh (16kHz中文识别模型)
     * - 音频格式：PCM格式 (值为1)
     * - VAD开启：自动检测语音活动，提高识别准确性
     * - 请求ID：UUID确保每个请求的唯一性
     *
     * 模型选择考虑：
     * - 16k_zh：适合高质量音频的中文识别
     * - 支持普通话识别，适合面试场景
     * - 平衡识别准确性和响应速度
     *
     * @return 配置完成的语音识别请求对象
     */
    private SpeechRecognizerRequest makeRequest() {
        // 初始化请求对象
        SpeechRecognizerRequest request = SpeechRecognizerRequest.init();

        // 设置引擎模型类型为16kHz中文识别
        request.setEngineModelType("16k_zh");

        // 设置语音格式为PCM (1表示PCM格式)
        request.setVoiceFormat(1);

        // 设置唯一请求标识，用于追踪和调试
        request.setVoiceId(UUID.randomUUID().toString());

        // 开启VAD (Voice Activity Detection) 语音活动检测
        // 1表示开启，可以自动检测语音的开始和结束
        request.setNeedVad(1);

        return request;
    }

    /**
     * 设置Spring应用上下文
     *
     * 功能说明：
     * 1. 由主启动类调用，传入Spring应用上下文
     * 2. 使WebSocket端点能够访问Spring管理的Bean
     * 3. 解决WebSocket端点不受Spring管理的依赖注入问题
     *
     * 技术背景：
     * - WebSocket端点由Servlet容器管理，不是Spring Bean
     * - 无法直接使用@Autowired等Spring注解
     * - 需要手动获取Spring容器中的Bean
     *
     * @param context Spring应用上下文对象
     */
    public static void setApplicationContext(ApplicationContext context) {
        applicationContext = context;
    }

    /**
     * 初始化Spring Bean依赖
     */
    private void initializeDependencies() {
        if (applicationContext != null) {
            this.tencentSpeechConfig = applicationContext.getBean(TencentSpeechConfig.class);
            this.tencentSpeechRecognizerListener = applicationContext.getBean(TencentSpeechRecognizerListener.class);
            this.jwtUtil = applicationContext.getBean(JwtUtil.class);
            this.userService = applicationContext.getBean(UserService.class);
            this.speechSessionService = applicationContext.getBean(SpeechSessionService.class);
            this.audioFileService = applicationContext.getBean(AudioFileService.class);
        }
    }

    /**
     * 处理用户认证
     */
    private void handleAuthentication(JSONObject data, Session session) {
        try {
            String token = data.getString("token");
            if (token == null || token.trim().isEmpty()) {
                sendMessage(createMessage("AUTH_ERROR", "MISSING_TOKEN", "缺少认证令牌"));
                return;
            }

            // 验证JWT令牌
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null || !jwtUtil.validateToken(token, username)) {
                sendMessage(createMessage("AUTH_ERROR", "INVALID_TOKEN", "无效的认证令牌"));
                return;
            }

            // 获取用户信息
            currentUser = userService.getByUsername(username);
            if (currentUser == null) {
                sendMessage(createMessage("AUTH_ERROR", "USER_NOT_FOUND", "用户不存在"));
                return;
            }

            // 检查用户状态
            if (currentUser.getStatus() != 1) {
                sendMessage(createMessage("AUTH_ERROR", "USER_DISABLED", "用户已被禁用"));
                return;
            }

            // 检查用户时长
            if (!speechSessionService.hasEnoughDuration(currentUser.getId())) {
                long remainingDuration = speechSessionService.getRemainingDuration(currentUser.getId());
                sendMessage(createMessage("AUTH_ERROR", "INSUFFICIENT_DURATION",
                    "用户时长不足，剩余: " + remainingDuration + "秒"));
                return;
            }

            // 创建语音识别会话
            String clientIp = getClientIp(session);
            String userAgent = getUserAgent(session);
            currentSpeechSession = speechSessionService.createSession(currentUser, session.getId(), clientIp, userAgent);

            if (currentSpeechSession == null) {
                sendMessage(createMessage("AUTH_ERROR", "SESSION_CREATE_FAILED", "创建会话失败"));
                return;
            }

            // 创建录音文件
            try {
                currentAudioFilePath = audioFileService.createAudioFile(
                    currentSpeechSession.getSessionId(), currentUser.getId());
                log.info("【创建录音文件】路径: {}", currentAudioFilePath);
            } catch (IOException e) {
                log.error("【创建录音文件失败】", e);
                // 录音文件创建失败不影响语音识别功能
            }

            // 认证成功，初始化语音识别器
            initializeSpeechRecognizer();

            authenticated = true;
            sessionStartTime = System.currentTimeMillis();

            long remainingDuration = speechSessionService.getRemainingDuration(currentUser.getId());

            JSONObject authSuccessData = new JSONObject();
            authSuccessData.put("sessionId", currentSpeechSession.getSessionId());
            authSuccessData.put("username", currentUser.getUsername());
            authSuccessData.put("remainingDuration", remainingDuration);

            sendMessage(createMessage("AUTH_SUCCESS", "AUTHENTICATED", "认证成功", authSuccessData));

            log.info("【用户认证成功】用户: {}, 会话ID: {}, 剩余时长: {}s",
                currentUser.getUsername(), currentSpeechSession.getSessionId(), remainingDuration);

            // 启动时长监控
            startDurationMonitoring();

        } catch (Exception e) {
            log.error("【认证处理失败】", e);
            sendMessage(createMessage("AUTH_ERROR", "AUTH_PROCESS_ERROR", "认证处理失败"));
        }
    }

    /**
     * 处理音频数据消息
     */
    private void handleAudioData(JSONObject data, Session session) {
        if (!authenticated) {
            sendMessage(createMessage("ERROR", "NOT_AUTHENTICATED", "请先进行认证"));
            return;
        }

        try {
            String base64Data = data.getString("data");
            if (base64Data != null) {
                byte[] audioData = Base64.getDecoder().decode(base64Data);

                // 写入录音文件
                if (currentAudioFilePath != null) {
                    audioFileService.writeAudioData(currentAudioFilePath, audioData);
                }

                // 发送到腾讯云ASR进行识别
                if (speechRecognizer != null) {
                    speechRecognizer.write(audioData);
                }

                log.debug("【处理Base64音频数据】大小: {}bytes", audioData.length);
            }
        } catch (Exception e) {
            log.error("【处理音频数据消息失败】", e);
            sendMessage(createMessage("ERROR", "AUDIO_DATA_ERROR", "音频数据处理失败"));
        }
    }

    /**
     * 处理结束会话消息
     */
    private void handleEndSession(JSONObject data, Session session) {
        if (!authenticated) {
            return;
        }

        String reason = data.getString("reason");
        endSession(SpeechSession.EndReason.NORMAL, reason != null ? reason : "用户主动结束");
    }

    /**
     * 初始化语音识别器
     */
    private void initializeSpeechRecognizer() throws Exception {
        log.info("【初始化语音识别器】开始初始化腾讯云语音识别服务");

        // 从Spring容器获取配置Bean
        if (tencentSpeechConfig == null) {
            tencentSpeechConfig = applicationContext.getBean(TencentSpeechConfig.class);
        }
        if (tencentSpeechRecognizerListener == null) {
            tencentSpeechRecognizerListener = applicationContext.getBean(TencentSpeechRecognizerListener.class);
        }

        // 创建腾讯云认证凭据
        Credential credential = new Credential(
                tencentSpeechConfig.getAppId(),
                tencentSpeechConfig.getSecretId(),
                tencentSpeechConfig.getSecretKey()
        );


        // 配置语音识别请求参数
        SpeechRecognizerRequest request = makeRequest();

        // 创建语音识别器实例
        speechRecognizer = new SpeechRecognizer(speechClient,credential, request, tencentSpeechRecognizerListener);

        // 启动语音识别服务
        speechRecognizer.start();

        log.info("【初始化语音识别器】腾讯云语音识别服务启动成功");
    }

    /**
     * 启动时长监控
     */
    private void startDurationMonitoring() {
        scheduler.scheduleAtFixedRate(() -> {
            if (!authenticated || currentSpeechSession == null) {
                return;
            }

            try {
                long currentDuration = (System.currentTimeMillis() - sessionStartTime) / 1000;
                long remainingDuration = speechSessionService.getRemainingDuration(currentUser.getId());

                // 检查是否超时
                if (currentDuration >= remainingDuration) {
                    log.info("【时长监控】用户时长耗尽，自动结束会话");
                    endSession(SpeechSession.EndReason.NO_DURATION, "用户时长耗尽");
                    return;
                }

                // 发送剩余时长提醒（剩余时间少于60秒时）
                long remaining = remainingDuration - currentDuration;
                if (remaining <= 60 && remaining % 10 == 0) {
                    JSONObject warningData = new JSONObject();
                    warningData.put("remainingSeconds", remaining);
                    sendMessage(createMessage("DURATION_WARNING", "LOW_DURATION",
                        "剩余时长不足: " + remaining + "秒", warningData));
                }

            } catch (Exception e) {
                log.error("【时长监控异常】", e);
            }
        }, 5, 5, TimeUnit.SECONDS);
    }

    /**
     * 结束语音识别会话
     */
    private void endSession(String endReason, String remarks) {
        if (currentSpeechSession == null) {
            return;
        }

        try {
            log.info("【结束语音会话】会话ID: {}, 原因: {}", currentSpeechSession.getSessionId(), endReason);

            // 关闭录音文件
            if (currentAudioFilePath != null) {
                try {
                    long fileSize = audioFileService.closeAudioFile(currentAudioFilePath);
                    speechSessionService.saveAudioFile(currentSpeechSession.getSessionId(), currentAudioFilePath, fileSize);
                    log.info("【录音文件已保存】路径: {}, 大小: {}bytes", currentAudioFilePath, fileSize);
                } catch (IOException e) {
                    log.error("【关闭录音文件失败】", e);
                }
            }

            // 结束会话
            speechSessionService.endSession(currentSpeechSession.getSessionId(), endReason, remarks);

            // 发送会话结束消息
            JSONObject endData = new JSONObject();
            endData.put("sessionId", currentSpeechSession.getSessionId());
            endData.put("endReason", endReason);
            endData.put("duration", (System.currentTimeMillis() - sessionStartTime) / 1000);

            sendMessage(createMessage("SESSION_ENDED", endReason, remarks, endData));

            // 关闭WebSocket连接
            closeConnection(remarks);

        } catch (Exception e) {
            log.error("【结束会话失败】", e);
        }
    }

    /**
     * 发送消息到客户端
     */
    private void sendMessage(String message) {
        if (websocketSession != null && websocketSession.isOpen()) {
            try {
                websocketSession.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("【发送消息失败】", e);
            }
        }
    }

    /**
     * 创建标准格式的消息
     */
    private String createMessage(String type, String status, String message) {
        return createMessage(type, status, message, null);
    }

    /**
     * 创建标准格式的消息
     */
    private String createMessage(String type, String status, String message, JSONObject data) {
        JSONObject response = new JSONObject();
        response.put("type", type);
        response.put("status", status);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());

        if (data != null) {
            response.put("data", data);
        }

        return response.toJSONString();
    }

    /**
     * 关闭WebSocket连接
     */
    private void closeConnection(String reason) {
        if (websocketSession != null && websocketSession.isOpen()) {
            try {
                websocketSession.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, reason));
            } catch (IOException e) {
                log.error("【关闭连接失败】", e);
            }
        }
    }

    /**
     * 清理资源
     */
    private void cleanup(String sessionId) {
        // 从活跃会话中移除
        activeSessions.remove(sessionId);

        // 清理录音文件流
        if (currentAudioFilePath != null) {
            try {
                audioFileService.closeAudioFile(currentAudioFilePath);
            } catch (IOException e) {
                log.error("【清理录音文件失败】", e);
            }
        }

        // 重置状态
        authenticated = false;
        currentUser = null;
        currentSpeechSession = null;
        currentAudioFilePath = null;

        log.info("【资源清理完成】会话ID: {}", sessionId);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(Session session) {
        try {
            return session.getUserProperties().get("javax.websocket.endpoint.remoteAddress").toString();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取用户代理信息
     */
    private String getUserAgent(Session session) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, java.util.List<String>> headers = (Map<String, java.util.List<String>>)
                session.getUserProperties().get("org.apache.tomcat.websocket.HTTP_HEADERS");
            if (headers != null && headers.containsKey("user-agent")) {
                return headers.get("user-agent").get(0);
            }
        } catch (Exception e) {
            // ignore
        }
        return "unknown";
    }

    /**
     * 获取所有活跃会话数量
     */
    public static int getActiveSessionCount() {
        return activeSessions.size();
    }

    /**
     * 强制关闭指定用户的所有会话
     */
    public static void forceCloseUserSessions(Long userId, String reason) {
        activeSessions.values().forEach(session -> {
            if (session.currentUser != null && session.currentUser.getId().equals(userId)) {
                session.endSession(SpeechSession.EndReason.FORCE, reason);
            }
        });
    }


}
