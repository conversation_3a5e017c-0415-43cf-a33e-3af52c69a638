-- =====================================================
-- 1. 用户积分系统 - 主要建表脚本
-- 执行顺序：第一个执行
-- 创建时间：2025-08-04
-- 功能说明：用户积分管理、充值记录、消费记录、充值码管理
-- =====================================================

-- 1.1 修改用户表，添加积分字段
-- 注意：如果字段已存在会报错，可以忽略
ALTER TABLE `sys_user` ADD COLUMN `points` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户积分余额' AFTER `status`;

-- 1.2 创建积分充值记录表
DROP TABLE IF EXISTS `user_points_recharge`;
CREATE TABLE `user_points_recharge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `recharge_type` tinyint(1) NOT NULL COMMENT '充值类型：1-充值码充值，2-管理员直充',
  `recharge_code` varchar(100) DEFAULT NULL COMMENT '充值码（充值码充值时使用）',
  `points_amount` bigint(20) NOT NULL COMMENT '充值积分数量',
  `points_before` bigint(20) NOT NULL COMMENT '充值前积分余额',
  `points_after` bigint(20) NOT NULL COMMENT '充值后积分余额',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '充值金额（如果有的话）',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作员ID（管理员直充时记录）',
  `operator_username` varchar(50) DEFAULT NULL COMMENT '操作员用户名',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '充值状态：0-失败，1-成功，2-处理中',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_recharge_type` (`recharge_type`),
  KEY `idx_recharge_code` (`recharge_code`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_user_recharge_time` (`user_id`, `status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分充值记录表';

-- 1.3 创建积分消费记录表
DROP TABLE IF EXISTS `user_points_consumption`;
CREATE TABLE `user_points_consumption` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `consumption_type` tinyint(1) NOT NULL COMMENT '消费类型：1-面试功能，2-语音识别，3-AI答复，4-其他',
  `points_amount` bigint(20) NOT NULL COMMENT '消费积分数量',
  `points_before` bigint(20) NOT NULL COMMENT '消费前积分余额',
  `points_after` bigint(20) NOT NULL COMMENT '消费后积分余额',
  `business_id` varchar(100) DEFAULT NULL COMMENT '关联业务ID（如面试会话ID、语音识别任务ID等）',
  `business_desc` varchar(200) DEFAULT NULL COMMENT '业务描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消费状态：0-失败，1-成功，2-退款',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_consumption_type` (`consumption_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_user_consumption_time` (`user_id`, `status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分消费记录表';

-- 1.4 创建充值码表
DROP TABLE IF EXISTS `recharge_code`;
CREATE TABLE `recharge_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(200) NOT NULL COMMENT '充值码（加密后的）',
  `points_amount` bigint(20) NOT NULL COMMENT '积分数量',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '充值金额',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用状态：0-未使用，1-已使用，2-已过期',
  `used_user_id` bigint(20) DEFAULT NULL COMMENT '使用用户ID',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `batch_no` varchar(50) DEFAULT NULL COMMENT '生成批次号',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `creator_username` varchar(50) DEFAULT NULL COMMENT '创建者用户名',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_used_user_id` (`used_user_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_code_status_expire` (`status`, `expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值码表';

-- 1.5 执行完成提示
SELECT '1_user_points_system.sql 执行完成！' as message;
SELECT 'Tables created: user_points_recharge, user_points_consumption, recharge_code' as info;
SELECT 'sys_user table modified: added points column' as info;
