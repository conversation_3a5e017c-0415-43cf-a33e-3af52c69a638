-- =====================================================
-- 2. 初始化测试数据（可选）
-- 执行顺序：第二个执行（可选）
-- 创建时间：2025-08-04
-- 功能说明：为用户积分系统初始化测试数据
-- =====================================================

-- 2.1 为现有用户初始化积分
-- 给所有现有用户初始化100积分
UPDATE `sys_user` 
SET `points` = 100 
WHERE `points` = 0 AND `deleted` = 0;

-- 2.2 插入测试充值码
-- 插入几个测试充值码供测试使用
INSERT INTO `recharge_code` (
  `code`, `points_amount`, `amount`, `status`, `expire_time`, 
  `batch_no`, `creator_id`, `creator_username`, `remark`
) VALUES 
(
  'dGVzdGNvZGUxMjM0NTY3OA==', -- 这是加密后的测试充值码
  1000, 
  10.00, 
  0, 
  DATE_ADD(NOW(), INTERVAL 30 DAY),
  'BATCH_20250804120000', 
  1, 
  'admin', 
  '测试充值码 - 1000积分'
),
(
  'dGVzdGNvZGU5ODc2NTQzMjE=', -- 这是加密后的测试充值码
  500, 
  5.00, 
  0, 
  DATE_ADD(NOW(), INTERVAL 30 DAY),
  'BATCH_20250804120000', 
  1, 
  'admin', 
  '测试充值码 - 500积分'
),
(
  'dGVzdGNvZGVhYmNkZWZnaA==', -- 这是加密后的测试充值码
  2000, 
  20.00, 
  0, 
  DATE_ADD(NOW(), INTERVAL 30 DAY),
  'BATCH_20250804120000', 
  1, 
  'admin', 
  '测试充值码 - 2000积分'
);

-- 2.3 插入测试消费记录（模拟一些历史消费）
-- 注意：这里需要确保user_id存在于sys_user表中
INSERT INTO `user_points_consumption` (
  `user_id`, `consumption_type`, `points_amount`, `points_before`, `points_after`,
  `business_id`, `business_desc`, `status`, `remark`
) 
SELECT 
  u.id as user_id,
  1 as consumption_type, -- 面试功能
  10 as points_amount,
  110 as points_before,
  100 as points_after,
  CONCAT('INTERVIEW_', u.id, '_', UNIX_TIMESTAMP()) as business_id,
  '测试面试会话' as business_desc,
  1 as status,
  '测试数据 - 面试功能消费' as remark
FROM sys_user u 
WHERE u.deleted = 0 
LIMIT 3; -- 只为前3个用户创建测试数据

-- 2.4 插入测试充值记录（模拟一些历史充值）
INSERT INTO `user_points_recharge` (
  `user_id`, `recharge_type`, `points_amount`, `points_before`, `points_after`,
  `operator_id`, `operator_username`, `status`, `remark`
) 
SELECT 
  u.id as user_id,
  2 as recharge_type, -- 管理员直充
  100 as points_amount,
  0 as points_before,
  100 as points_after,
  1 as operator_id,
  'admin' as operator_username,
  1 as status,
  '测试数据 - 管理员初始化充值' as remark
FROM sys_user u 
WHERE u.deleted = 0 
LIMIT 5; -- 为前5个用户创建充值记录

-- 2.5 执行完成提示
SELECT '2_init_test_data.sql 执行完成！' as message;
SELECT CONCAT('已为 ', COUNT(*), ' 个用户初始化积分') as info FROM sys_user WHERE points > 0 AND deleted = 0;
SELECT CONCAT('已创建 ', COUNT(*), ' 个测试充值码') as info FROM recharge_code WHERE deleted = 0;
SELECT CONCAT('已创建 ', COUNT(*), ' 条测试消费记录') as info FROM user_points_consumption WHERE deleted = 0;
SELECT CONCAT('已创建 ', COUNT(*), ' 条测试充值记录') as info FROM user_points_recharge WHERE deleted = 0;
