-- =====================================================
-- 3. 验证安装结果
-- 执行顺序：第三个执行（验证用）
-- 创建时间：2025-08-04
-- 功能说明：验证用户积分系统是否正确安装
-- =====================================================

-- 3.1 验证sys_user表是否有points字段
SELECT '=== 验证sys_user表points字段 ===' as step;
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sys_user' 
  AND COLUMN_NAME = 'points';

-- 3.2 验证新建表是否存在
SELECT '=== 验证新建表 ===' as step;
SELECT 
    TABLE_NAME as '表名',
    ENGINE as '存储引擎',
    TABLE_COLLATION as '字符集',
    TABLE_COMMENT as '表注释'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('user_points_recharge', 'user_points_consumption', 'recharge_code')
ORDER BY TABLE_NAME;

-- 3.3 验证表结构
SELECT '=== 验证user_points_recharge表结构 ===' as step;
DESCRIBE user_points_recharge;

SELECT '=== 验证user_points_consumption表结构 ===' as step;
DESCRIBE user_points_consumption;

SELECT '=== 验证recharge_code表结构 ===' as step;
DESCRIBE recharge_code;

-- 3.4 验证索引
SELECT '=== 验证索引 ===' as step;
SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    COLUMN_NAME as '字段名',
    NON_UNIQUE as '非唯一'
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('user_points_recharge', 'user_points_consumption', 'recharge_code')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 3.5 统计数据量
SELECT '=== 数据统计 ===' as step;
SELECT 'sys_user' as '表名', COUNT(*) as '记录数', SUM(points) as '总积分' FROM sys_user WHERE deleted = 0
UNION ALL
SELECT 'user_points_recharge' as '表名', COUNT(*) as '记录数', SUM(points_amount) as '总积分' FROM user_points_recharge WHERE deleted = 0
UNION ALL
SELECT 'user_points_consumption' as '表名', COUNT(*) as '记录数', SUM(points_amount) as '总积分' FROM user_points_consumption WHERE deleted = 0
UNION ALL
SELECT 'recharge_code' as '表名', COUNT(*) as '记录数', SUM(points_amount) as '总积分' FROM recharge_code WHERE deleted = 0;

-- 3.6 验证充值码状态分布
SELECT '=== 充值码状态分布 ===' as step;
SELECT 
    CASE status 
        WHEN 0 THEN '未使用'
        WHEN 1 THEN '已使用'
        WHEN 2 THEN '已过期'
        ELSE '未知'
    END as '状态',
    COUNT(*) as '数量',
    SUM(points_amount) as '总积分'
FROM recharge_code 
WHERE deleted = 0
GROUP BY status;

-- 3.7 验证消费类型分布
SELECT '=== 消费类型分布 ===' as step;
SELECT 
    CASE consumption_type 
        WHEN 1 THEN '面试功能'
        WHEN 2 THEN '语音识别'
        WHEN 3 THEN 'AI答复'
        WHEN 4 THEN '其他'
        ELSE '未知'
    END as '消费类型',
    COUNT(*) as '消费次数',
    SUM(points_amount) as '总消费积分'
FROM user_points_consumption 
WHERE deleted = 0 AND status = 1
GROUP BY consumption_type;

-- 3.8 验证用户积分分布
SELECT '=== 用户积分分布 ===' as step;
SELECT 
    CASE 
        WHEN points = 0 THEN '0积分'
        WHEN points BETWEEN 1 AND 50 THEN '1-50积分'
        WHEN points BETWEEN 51 AND 100 THEN '51-100积分'
        WHEN points BETWEEN 101 AND 500 THEN '101-500积分'
        WHEN points > 500 THEN '500+积分'
        ELSE '其他'
    END as '积分范围',
    COUNT(*) as '用户数量'
FROM sys_user 
WHERE deleted = 0
GROUP BY 
    CASE 
        WHEN points = 0 THEN '0积分'
        WHEN points BETWEEN 1 AND 50 THEN '1-50积分'
        WHEN points BETWEEN 51 AND 100 THEN '51-100积分'
        WHEN points BETWEEN 101 AND 500 THEN '101-500积分'
        WHEN points > 500 THEN '500+积分'
        ELSE '其他'
    END
ORDER BY MIN(points);

-- 3.9 最终验证结果
SELECT '=== 安装验证完成 ===' as step;
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'sys_user' AND COLUMN_NAME = 'points') > 0
         AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user_points_recharge') > 0
         AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user_points_consumption') > 0
         AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'recharge_code') > 0
        THEN '✅ 用户积分系统安装成功！'
        ELSE '❌ 用户积分系统安装失败，请检查错误信息'
    END as '安装结果';

SELECT '验证完成时间：' as info, NOW() as '时间';
