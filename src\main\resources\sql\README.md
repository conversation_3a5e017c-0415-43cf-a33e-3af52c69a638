# 用户积分系统 SQL 执行说明

## 📋 文件执行顺序

请按照以下顺序执行SQL文件：

### 1️⃣ 主要建表脚本（必须执行）
**文件名：** `1_user_points_system.sql`
**功能：** 创建用户积分系统的核心表结构
**内容：**
- 为 `sys_user` 表添加 `points` 字段
- 创建 `user_points_recharge` 表（积分充值记录）
- 创建 `user_points_consumption` 表（积分消费记录）
- 创建 `recharge_code` 表（充值码管理）

### 2️⃣ 测试数据初始化（可选执行）
**文件名：** `2_init_test_data.sql`
**功能：** 初始化测试数据，便于开发和测试
**内容：**
- 为现有用户初始化100积分
- 插入3个测试充值码
- 创建模拟的消费记录
- 创建模拟的充值记录

### 3️⃣ 安装验证脚本（建议执行）
**文件名：** `3_verify_installation.sql`
**功能：** 验证积分系统是否正确安装
**内容：**
- 验证表结构是否正确
- 检查索引是否创建成功
- 统计数据分布情况
- 输出安装结果报告

## 🚀 执行命令

### 方式一：命令行执行
```bash
# 1. 执行主要建表脚本
mysql -u root -p jobplusv8 < src/main/resources/sql/1_user_points_system.sql

# 2. 执行测试数据初始化（可选）
mysql -u root -p jobplusv8 < src/main/resources/sql/2_init_test_data.sql

# 3. 执行验证脚本
mysql -u root -p jobplusv8 < src/main/resources/sql/3_verify_installation.sql
```

### 方式二：MySQL客户端执行
```sql
-- 连接数据库
USE jobplusv8;

-- 执行脚本
SOURCE src/main/resources/sql/1_user_points_system.sql;
SOURCE src/main/resources/sql/2_init_test_data.sql;
SOURCE src/main/resources/sql/3_verify_installation.sql;
```

### 方式三：一键执行所有脚本
```bash
# 创建一键执行脚本
cat src/main/resources/sql/1_user_points_system.sql \
    src/main/resources/sql/2_init_test_data.sql \
    src/main/resources/sql/3_verify_installation.sql \
    | mysql -u root -p jobplusv8
```

## ⚠️ 注意事项

### 1. 字段重复添加
如果 `sys_user` 表已经有 `points` 字段，执行第一个脚本时会报错：
```
ERROR 1060 (42S21): Duplicate column name 'points'
```
**解决方案：** 忽略此错误，继续执行后续SQL即可。

### 2. 表已存在
脚本中使用了 `DROP TABLE IF EXISTS`，所以重复执行不会有问题。

### 3. 权限要求
确保数据库用户有以下权限：
- `CREATE` - 创建表
- `ALTER` - 修改表结构
- `INSERT` - 插入数据
- `SELECT` - 查询数据
- `UPDATE` - 更新数据
- `INDEX` - 创建索引

### 4. 数据库版本
建议使用 MySQL 5.7+ 或 MariaDB 10.2+

## 📊 验证结果说明

执行 `3_verify_installation.sql` 后，会看到以下验证信息：

### ✅ 成功标志
- `sys_user` 表有 `points` 字段
- 3个新表创建成功：`user_points_recharge`、`user_points_consumption`、`recharge_code`
- 所有索引创建成功
- 最终显示：`✅ 用户积分系统安装成功！`

### 📈 数据统计
- 用户积分分布情况
- 充值码状态分布
- 消费类型分布
- 各表记录数统计

## 🔧 故障排除

### 问题1：字符集错误
```sql
-- 检查数据库字符集
SHOW CREATE DATABASE jobplusv8;

-- 如果不是utf8mb4，需要修改
ALTER DATABASE jobplusv8 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题2：存储引擎不支持
```sql
-- 检查是否支持InnoDB
SHOW ENGINES;

-- 如果不支持，修改建表语句中的ENGINE=MyISAM
```

### 问题3：索引创建失败
```sql
-- 检查索引是否创建成功
SHOW INDEX FROM user_points_recharge;
SHOW INDEX FROM user_points_consumption;
SHOW INDEX FROM recharge_code;
```

## 📝 执行日志示例

正常执行后应该看到类似输出：
```
Query OK, 0 rows affected (0.01 sec)  -- 添加points字段
Query OK, 0 rows affected (0.02 sec)  -- 创建user_points_recharge表
Query OK, 0 rows affected (0.02 sec)  -- 创建user_points_consumption表
Query OK, 0 rows affected (0.02 sec)  -- 创建recharge_code表
...
+----------------------------------+
| message                          |
+----------------------------------+
| ✅ 用户积分系统安装成功！          |
+----------------------------------+
```

## 🎯 下一步

SQL执行完成后：
1. 启动Spring Boot应用
2. 访问用户中心页面：`http://localhost:80/user-center.html`
3. 测试积分充值和消费功能
4. 查看API文档：`http://localhost:80/swagger-ui.html`

---

**执行完成后，用户积分系统就可以正常使用了！** 🎉
