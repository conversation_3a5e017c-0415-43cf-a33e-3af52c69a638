/**
 * 统一Header组件
 * 提供导航菜单、用户信息、登录状态等功能
 */

class HeaderComponent {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.init();
    }

    init() {
        this.checkLoginStatus();
        this.render();
        this.bindEvents();
    }

    // 检查登录状态
    checkLoginStatus() {
        const token = localStorage.getItem('token');
        if (token) {
            this.isLoggedIn = true;
            // 可以在这里验证token有效性
        }
    }

    // 渲染Header
    render() {
        const headerHTML = `
            <div class="header-component">
                <div class="header-container">
                    <div class="header-left">
                        <div class="logo">
                            <a href="/">
                                <h1>JobPlusV8</h1>
                                <span>智能面试系统</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="header-center">
                        <nav class="main-nav">
                            <a href="/" class="nav-item">
                                <i class="icon">🏠</i>
                                <span>首页</span>
                            </a>
                            <a href="/user-center.html" class="nav-item">
                                <i class="icon">👤</i>
                                <span>用户中心</span>
                            </a>
                            <a href="/interview-records.html" class="nav-item">
                                <i class="icon">📝</i>
                                <span>面试记录</span>
                            </a>
                            <a href="/resume-upload.html" class="nav-item">
                                <i class="icon">📄</i>
                                <span>简历管理</span>
                            </a>
                            <a href="/swagger-ui.html" class="nav-item" target="_blank">
                                <i class="icon">📚</i>
                                <span>API文档</span>
                            </a>
                        </nav>
                    </div>
                    
                    <div class="header-right">
                        ${this.isLoggedIn ? this.renderUserMenu() : this.renderLoginButton()}
                    </div>
                </div>
            </div>
        `;

        // 插入到页面顶部
        const existingHeader = document.querySelector('.header-component');
        if (existingHeader) {
            existingHeader.remove();
        }
        
        document.body.insertAdjacentHTML('afterbegin', headerHTML);
        this.addStyles();
    }

    // 渲染用户菜单
    renderUserMenu() {
        return `
            <div class="user-menu">
                <div class="user-info">
                    <span class="user-avatar">👤</span>
                    <span class="user-name">用户</span>
                    <span class="dropdown-arrow">▼</span>
                </div>
                <div class="user-dropdown">
                    <a href="/user-center.html" class="dropdown-item">
                        <i class="icon">⚙️</i>
                        <span>个人设置</span>
                    </a>
                    <a href="#" class="dropdown-item" onclick="headerComponent.logout()">
                        <i class="icon">🚪</i>
                        <span>退出登录</span>
                    </a>
                </div>
            </div>
        `;
    }

    // 渲染登录按钮
    renderLoginButton() {
        return `
            <div class="login-section">
                <a href="/login.html" class="login-btn">登录</a>
                <a href="/register.html" class="register-btn">注册</a>
            </div>
        `;
    }

    // 添加样式
    addStyles() {
        if (document.querySelector('#header-component-styles')) {
            return;
        }

        const styles = `
            <style id="header-component-styles">
                .header-component {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    position: sticky;
                    top: 0;
                    z-index: 1000;
                }

                .header-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 20px;
                    height: 70px;
                }

                .header-left .logo a {
                    display: flex;
                    align-items: center;
                    text-decoration: none;
                    color: white;
                }

                .header-left .logo h1 {
                    margin: 0;
                    font-size: 1.8em;
                    font-weight: 600;
                    margin-right: 10px;
                }

                .header-left .logo span {
                    font-size: 0.9em;
                    opacity: 0.8;
                }

                .main-nav {
                    display: flex;
                    gap: 30px;
                }

                .nav-item {
                    display: flex;
                    align-items: center;
                    text-decoration: none;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 6px;
                    transition: all 0.3s ease;
                    font-size: 0.95em;
                }

                .nav-item:hover {
                    background: rgba(255,255,255,0.1);
                    transform: translateY(-1px);
                }

                .nav-item.active {
                    background: rgba(255,255,255,0.2);
                }

                .nav-item .icon {
                    margin-right: 8px;
                    font-size: 1.1em;
                }

                .user-menu {
                    position: relative;
                }

                .user-info {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    padding: 8px 16px;
                    border-radius: 6px;
                    transition: background 0.3s ease;
                }

                .user-info:hover {
                    background: rgba(255,255,255,0.1);
                }

                .user-avatar {
                    margin-right: 8px;
                    font-size: 1.2em;
                }

                .user-name {
                    margin-right: 8px;
                }

                .dropdown-arrow {
                    font-size: 0.8em;
                    transition: transform 0.3s ease;
                }

                .user-dropdown {
                    position: absolute;
                    top: 100%;
                    right: 0;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
                    min-width: 180px;
                    opacity: 0;
                    visibility: hidden;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    margin-top: 5px;
                }

                .user-menu:hover .user-dropdown {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                }

                .user-menu:hover .dropdown-arrow {
                    transform: rotate(180deg);
                }

                .dropdown-item {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    text-decoration: none;
                    color: #333;
                    transition: background 0.3s ease;
                }

                .dropdown-item:hover {
                    background: #f8f9fa;
                }

                .dropdown-item .icon {
                    margin-right: 10px;
                }

                .login-section {
                    display: flex;
                    gap: 15px;
                }

                .login-btn, .register-btn {
                    padding: 8px 20px;
                    text-decoration: none;
                    border-radius: 6px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }

                .login-btn {
                    color: white;
                    border: 1px solid rgba(255,255,255,0.3);
                }

                .login-btn:hover {
                    background: rgba(255,255,255,0.1);
                }

                .register-btn {
                    background: rgba(255,255,255,0.2);
                    color: white;
                }

                .register-btn:hover {
                    background: rgba(255,255,255,0.3);
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .header-container {
                        padding: 0 15px;
                    }
                    
                    .main-nav {
                        display: none;
                    }
                    
                    .header-left .logo h1 {
                        font-size: 1.5em;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    // 绑定事件
    bindEvents() {
        // 设置当前页面的导航项为激活状态
        this.setActiveNavItem();
    }

    // 设置激活的导航项
    setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href === currentPath || (currentPath === '/' && href === '/')) {
                item.classList.add('active');
            }
        });
    }

    // 退出登录
    logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/';
    }

    // 更新用户信息
    updateUserInfo(userInfo) {
        this.currentUser = userInfo;
        this.isLoggedIn = true;
        this.render();
    }
}

// 全局实例
let headerComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    headerComponent = new HeaderComponent();
});
