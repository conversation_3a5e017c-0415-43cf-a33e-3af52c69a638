<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobPlusV8 - 面试语音识别系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #4facfe;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        .feature-card h3 {
            margin-top: 0;
            color: #333;
        }
        .api-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .api-link {
            display: block;
            padding: 15px 20px;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .api-link:hover {
            background: #3d8bfe;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        .websocket-info {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #4facfe;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>JobPlusV8</h1>
            <p>面试语音识别系统 - 基于腾讯云语音识别的实时转录解决方案</p>
        </div>

        <div class="content">
            <!-- 系统状态 -->
            <div class="section">
                <h2><span class="status-indicator"></span>系统状态</h2>
                <p>系统运行正常，所有服务可用</p>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🎤 语音识别服务</h3>
                        <p>基于腾讯云ASR，支持16kHz中文识别，准确率>95%</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔗 WebSocket通信</h3>
                        <p>实时音频流传输，低延迟响应，支持并发连接</p>
                    </div>
                    <div class="feature-card">
                        <h3>📚 API文档</h3>
                        <p>完整的Swagger文档，支持在线测试和调试</p>
                    </div>
                    <div class="feature-card">
                        <h3>📧 邮件服务</h3>
                        <p>验证码发送、面试邀请邮件，支持HTML模板</p>
                    </div>
                </div>
            </div>

            <!-- API文档和接口 -->
            <div class="section">
                <h2>📋 API接口和文档</h2>
                <div class="api-links">
                    <a href="/swagger-ui/index.html" class="api-link" target="_blank">
                        📚 Swagger API文档
                    </a>
                    <a href="/api/interview/health" class="api-link" target="_blank">
                        ❤️ 系统健康检查
                    </a>
                    <a href="/api/system/info" class="api-link" target="_blank">
                        ℹ️ 系统信息
                    </a>
                    <a href="/api/websocket/info" class="api-link" target="_blank">
                        🔌 WebSocket信息
                    </a>
                    <a href="/api/websocket/audio-format" class="api-link" target="_blank">
                        🎵 音频格式要求
                    </a>
                    <a href="/api/websocket/usage-guide" class="api-link" target="_blank">
                        📖 使用指南
                    </a>
                    <a href="/api/mail/send-code?email=<EMAIL>&purpose=REGISTER" class="api-link" target="_blank">
                        📧 发送验证码
                    </a>
                </div>
            </div>

            <!-- WebSocket连接信息 -->
            <div class="section">
                <h2>🔌 WebSocket连接</h2>
                <div class="websocket-info">
                    <h3>连接地址</h3>
                    <div class="code-block">ws://localhost:80/channel/echo</div>

                    <h3>音频格式要求</h3>
                    <ul>
                        <li><strong>编码格式:</strong> PCM</li>
                        <li><strong>采样率:</strong> 16000 Hz</li>
                        <li><strong>位深:</strong> 16 bit</li>
                        <li><strong>声道:</strong> 单声道 (Mono)</li>
                        <li><strong>字节序:</strong> Little Endian</li>
                    </ul>

                    <h3>JavaScript连接示例</h3>
                    <div class="code-block">
const ws = new WebSocket('ws://localhost:80/channel/echo');
ws.binaryType = 'arraybuffer';

ws.onopen = function() {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    console.log('收到消息:', event.data);
};

// 发送音频数据
ws.send(audioBuffer);
                    </div>
                </div>
            </div>

            <!-- 技术特性 -->
            <div class="section">
                <h2>⚡ 技术特性</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>实时处理</h3>
                        <p>毫秒级响应，支持实时语音转文字</p>
                    </div>
                    <div class="feature-card">
                        <h3>高准确率</h3>
                        <p>基于腾讯云ASR，中文识别准确率超过95%</p>
                    </div>
                    <div class="feature-card">
                        <h3>并发支持</h3>
                        <p>支持多个并发WebSocket连接</p>
                    </div>
                    <div class="feature-card">
                        <h3>完整文档</h3>
                        <p>详细的API文档和使用指南</p>
                    </div>
                    <div class="feature-card">
                        <h3>邮件通知</h3>
                        <p>验证码和面试邀请邮件发送</p>
                    </div>
                </div>
            </div>

            <!-- 版本信息 -->
            <div class="section">
                <h2>📦 版本信息</h2>
                <div class="feature-card">
                    <p><strong>版本:</strong> v0.0.1-SNAPSHOT</p>
                    <p><strong>构建时间:</strong> 2025-07-21</p>
                    <p><strong>技术栈:</strong> Spring Boot 2.7.6 + 腾讯云语音识别SDK + WebSocket</p>
                    <p><strong>开发团队:</strong> cloud.ipanda</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>