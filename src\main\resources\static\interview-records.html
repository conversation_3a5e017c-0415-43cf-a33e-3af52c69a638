<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面试记录 - JobPlusV8</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 0; /* header组件会处理顶部间距 */
        }

        .main-content {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .search-box {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .search-input {
            padding: 10px 15px;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            width: 250px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .records-grid {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }

        .record-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .record-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .record-meta {
            display: flex;
            gap: 20px;
            color: #666;
            font-size: 0.9em;
        }

        .record-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-in-progress {
            background: #fff3cd;
            color: #856404;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .record-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .detail-icon {
            font-size: 1.1em;
        }

        .detail-label {
            font-weight: 500;
            color: #666;
        }

        .detail-value {
            color: #333;
        }

        .record-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85em;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 30px;
        }

        .pagination button {
            margin: 0 5px;
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state .icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <!-- Header组件会自动插入到这里 -->
    
    <div class="main-content">
        <div class="page-header">
            <h1>📝 面试记录</h1>
            <p>查看和管理您的面试历史记录</p>
        </div>

        <div class="content">
            <div id="message" class="message"></div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索面试记录..." id="searchInput">
                    <button class="btn btn-primary" onclick="searchRecords()">
                        🔍 搜索
                    </button>
                </div>
                <div>
                    <a href="#" class="btn btn-success" onclick="createNewInterview()">
                        ➕ 新建面试
                    </a>
                </div>
            </div>

            <!-- 面试记录列表 -->
            <div id="recordsList" class="records-grid">
                <div class="loading">
                    <div>📊 正在加载面试记录...</div>
                </div>
            </div>

            <!-- 分页 -->
            <div id="pagination" class="pagination" style="display: none;">
                <!-- 分页按钮会动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入Header组件 -->
    <script src="/components/header.js"></script>
    
    <script>
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadInterviewRecords(1);
        });

        // 加载面试记录
        async function loadInterviewRecords(page = 1) {
            try {
                showLoading();
                
                // 模拟API调用 - 实际项目中替换为真实API
                const response = await mockApiCall(page);
                
                if (response.code === 200) {
                    renderRecords(response.data.records);
                    renderPagination(response.data);
                    currentPage = page;
                } else {
                    showMessage('加载失败：' + response.message, 'error');
                }
            } catch (error) {
                console.error('加载面试记录失败:', error);
                showMessage('加载失败，请稍后重试', 'error');
            }
        }

        // 模拟API调用
        async function mockApiCall(page) {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const mockData = {
                code: 200,
                message: '查询成功',
                data: {
                    current: page,
                    size: pageSize,
                    total: 25,
                    pages: 3,
                    records: generateMockRecords(page)
                }
            };
            
            return mockData;
        }

        // 生成模拟数据
        function generateMockRecords(page) {
            const records = [];
            const startIndex = (page - 1) * pageSize;
            
            for (let i = 0; i < pageSize && startIndex + i < 25; i++) {
                const index = startIndex + i + 1;
                records.push({
                    id: `SESSION_${Date.now()}_${index}`,
                    title: `Java开发工程师面试 #${index}`,
                    candidateName: `候选人${index}`,
                    interviewerName: `面试官${index % 3 + 1}`,
                    position: 'Java开发工程师',
                    status: ['completed', 'in-progress', 'cancelled'][index % 3],
                    createTime: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
                    duration: `${20 + index % 40}分钟`,
                    speechCount: 150 + index * 10,
                    totalWords: 2000 + index * 100
                });
            }
            
            return records;
        }

        // 渲染记录列表
        function renderRecords(records) {
            const container = document.getElementById('recordsList');
            
            if (records.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">📝</div>
                        <h3>暂无面试记录</h3>
                        <p>您还没有进行过面试，点击"新建面试"开始第一次面试吧！</p>
                    </div>
                `;
                return;
            }

            const recordsHTML = records.map(record => `
                <div class="record-card">
                    <div class="record-header">
                        <div>
                            <div class="record-title">${record.title}</div>
                            <div class="record-meta">
                                <span>📅 ${formatDate(record.createTime)}</span>
                                <span>⏱️ ${record.duration}</span>
                            </div>
                        </div>
                        <div class="record-status ${getStatusClass(record.status)}">
                            ${getStatusText(record.status)}
                        </div>
                    </div>
                    
                    <div class="record-details">
                        <div class="detail-item">
                            <span class="detail-icon">👤</span>
                            <span class="detail-label">候选人:</span>
                            <span class="detail-value">${record.candidateName}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">👨‍💼</span>
                            <span class="detail-label">面试官:</span>
                            <span class="detail-value">${record.interviewerName}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">💼</span>
                            <span class="detail-label">职位:</span>
                            <span class="detail-value">${record.position}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">🎤</span>
                            <span class="detail-label">识别次数:</span>
                            <span class="detail-value">${record.speechCount}次</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📝</span>
                            <span class="detail-label">总字数:</span>
                            <span class="detail-value">${record.totalWords}字</span>
                        </div>
                    </div>
                    
                    <div class="record-actions">
                        <button class="btn btn-outline btn-sm" onclick="viewRecord('${record.id}')">
                            👁️ 查看详情
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="downloadRecord('${record.id}')">
                            📥 下载记录
                        </button>
                        ${record.status === 'in-progress' ? 
                            `<button class="btn btn-primary btn-sm" onclick="continueInterview('${record.id}')">
                                ▶️ 继续面试
                            </button>` : ''
                        }
                    </div>
                </div>
            `).join('');

            container.innerHTML = recordsHTML;
        }

        // 渲染分页
        function renderPagination(data) {
            const container = document.getElementById('pagination');
            totalPages = data.pages;
            
            if (totalPages <= 1) {
                container.style.display = 'none';
                return;
            }
            
            container.style.display = 'flex';
            
            let paginationHTML = '';
            
            // 上一页
            if (currentPage > 1) {
                paginationHTML += `<button onclick="loadInterviewRecords(${currentPage - 1})">上一页</button>`;
            }
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHTML += `<button class="${activeClass}" onclick="loadInterviewRecords(${i})">${i}</button>`;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="loadInterviewRecords(${currentPage + 1})">下一页</button>`;
            }
            
            container.innerHTML = paginationHTML;
        }

        // 工具函数
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
        }

        function getStatusClass(status) {
            const statusMap = {
                'completed': 'status-completed',
                'in-progress': 'status-in-progress',
                'cancelled': 'status-cancelled'
            };
            return statusMap[status] || '';
        }

        function getStatusText(status) {
            const statusMap = {
                'completed': '已完成',
                'in-progress': '进行中',
                'cancelled': '已取消'
            };
            return statusMap[status] || '未知';
        }

        function showLoading() {
            document.getElementById('recordsList').innerHTML = `
                <div class="loading">
                    <div>📊 正在加载面试记录...</div>
                </div>
            `;
        }

        function showMessage(message, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        // 事件处理函数
        function searchRecords() {
            const keyword = document.getElementById('searchInput').value;
            showMessage(`搜索功能开发中，关键词：${keyword}`, 'success');
        }

        function createNewInterview() {
            showMessage('新建面试功能开发中', 'success');
        }

        function viewRecord(recordId) {
            showMessage(`查看记录详情：${recordId}`, 'success');
        }

        function downloadRecord(recordId) {
            showMessage(`下载记录：${recordId}`, 'success');
        }

        function continueInterview(recordId) {
            showMessage(`继续面试：${recordId}`, 'success');
        }
    </script>
</body>
</html>
