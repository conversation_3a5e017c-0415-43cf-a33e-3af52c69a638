<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历上传 - JobPlusV8</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 30px;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #28a745;
            background: #f0fff4;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #666;
            font-size: 0.9em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
            transform: scale(1.2);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            display: none;
        }

        .file-info h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .file-details {
            color: #666;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e1e5e9;
            border-radius: 3px;
            margin-top: 15px;
            overflow: hidden;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 简历上传</h1>
            <p>支持PDF、Word、TXT格式，AI智能解析</p>
        </div>
        
        <div class="content">
            <form id="uploadForm">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">拖拽文件到此处或点击选择</div>
                    <div class="upload-hint">支持 PDF、DOC、DOCX、TXT 格式，最大 10MB</div>
                    <input type="file" id="fileInput" accept=".pdf,.doc,.docx,.txt">
                </div>

                <div class="form-group">
                    <label for="resumeName">简历名称（姓名+岗位）</label>
                    <input type="text" id="resumeName" placeholder="例如：张三-Java开发工程师" required>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="isDefault">
                    <label for="isDefault">设为默认简历</label>
                </div>

                <button type="submit" class="btn" id="uploadBtn">
                    <span id="btnText">上传简历</span>
                </button>

                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div class="file-info" id="fileInfo">
                    <h4>选择的文件：</h4>
                    <div class="file-details" id="fileDetails"></div>
                </div>

                <div class="message" id="message"></div>
            </form>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadForm = document.getElementById('uploadForm');
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        const message = document.getElementById('message');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const uploadBtn = document.getElementById('uploadBtn');
        const btnText = document.getElementById('btnText');

        let selectedFile = null;

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // 点击选择文件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // 处理文件选择
        function handleFileSelect(file) {
            const allowedTypes = ['application/pdf', 'application/msword', 
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
                                'text/plain'];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (!allowedTypes.includes(file.type)) {
                showMessage('请选择PDF、Word或TXT格式的文件', 'error');
                return;
            }

            if (file.size > maxSize) {
                showMessage('文件大小不能超过10MB', 'error');
                return;
            }

            selectedFile = file;
            
            // 显示文件信息
            fileDetails.innerHTML = `
                <strong>文件名：</strong>${file.name}<br>
                <strong>大小：</strong>${formatFileSize(file.size)}<br>
                <strong>类型：</strong>${file.type}
            `;
            fileInfo.style.display = 'block';

            // 自动填充简历名称（去掉扩展名）
            const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
            if (!document.getElementById('resumeName').value) {
                document.getElementById('resumeName').value = nameWithoutExt;
            }

            hideMessage();
        }

        // 表单提交
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!selectedFile) {
                showMessage('请选择要上传的文件', 'error');
                return;
            }

            const resumeName = document.getElementById('resumeName').value.trim();
            if (!resumeName) {
                showMessage('请输入简历名称', 'error');
                return;
            }

            const isDefault = document.getElementById('isDefault').checked;

            // 创建FormData
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('resumeName', resumeName);
            formData.append('isDefault', isDefault);

            try {
                // 显示进度条
                progressBar.style.display = 'block';
                uploadBtn.disabled = true;
                btnText.textContent = '上传中...';

                // 模拟进度（实际项目中应该使用XMLHttpRequest监听进度）
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressFill.style.width = progress + '%';
                }, 200);

                // 发送请求（需要替换为实际的API地址和认证）
                const response = await fetch('/api/resume/upload', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        // 'Authorization': 'Bearer ' + getToken() // 需要添加认证token
                    }
                });

                clearInterval(progressInterval);
                progressFill.style.width = '100%';

                if (response.ok) {
                    const result = await response.json();
                    showMessage('简历上传成功！正在进行AI解析...', 'success');
                    
                    // 重置表单
                    setTimeout(() => {
                        resetForm();
                    }, 2000);
                } else {
                    const error = await response.json();
                    showMessage('上传失败：' + (error.message || '未知错误'), 'error');
                }

            } catch (error) {
                showMessage('上传失败：' + error.message, 'error');
            } finally {
                uploadBtn.disabled = false;
                btnText.textContent = '上传简历';
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 1000);
            }
        });

        // 显示消息
        function showMessage(text, type) {
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
        }

        // 隐藏消息
        function hideMessage() {
            message.style.display = 'none';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 重置表单
        function resetForm() {
            selectedFile = null;
            uploadForm.reset();
            fileInfo.style.display = 'none';
            hideMessage();
        }

        // 获取认证token（需要根据实际情况实现）
        function getToken() {
            return localStorage.getItem('token') || '';
        }
    </script>
</body>
</html>
