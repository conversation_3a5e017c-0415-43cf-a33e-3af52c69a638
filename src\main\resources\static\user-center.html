<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - JobPlusV8</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 30px;
        }

        .tab {
            padding: 15px 30px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 1.1em;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e1e5e9;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #666;
            font-weight: 500;
        }

        .info-value {
            color: #333;
            font-weight: 600;
        }

        .points-highlight {
            color: #667eea;
            font-size: 1.5em;
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .table th {
            background: #f8f9ff;
            font-weight: 600;
            color: #333;
        }

        .status-success {
            color: #28a745;
            font-weight: 600;
        }

        .status-failed {
            color: #dc3545;
            font-weight: 600;
        }

        .status-processing {
            color: #ffc107;
            font-weight: 600;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }

        .pagination button {
            margin: 0 5px;
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 用户中心</h1>
            <p>管理您的账号信息、积分和使用记录</p>
        </div>
        
        <div class="content">
            <div class="tabs">
                <button class="tab active" onclick="showTab('info')">账号信息</button>
                <button class="tab" onclick="showTab('password')">修改密码</button>
                <button class="tab" onclick="showTab('recharge')">积分充值</button>
                <button class="tab" onclick="showTab('recharge-records')">充值记录</button>
                <button class="tab" onclick="showTab('consumption-records')">消费记录</button>
            </div>

            <div class="message" id="message"></div>

            <!-- 账号信息 -->
            <div id="info-tab" class="tab-content active">
                <div class="info-grid">
                    <div class="info-card">
                        <h3>基本信息</h3>
                        <div class="info-item">
                            <span class="info-label">用户名</span>
                            <span class="info-value" id="username">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">邮箱</span>
                            <span class="info-value" id="email">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">手机号</span>
                            <span class="info-value" id="phone">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">真实姓名</span>
                            <span class="info-value" id="realName">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">账号状态</span>
                            <span class="info-value" id="statusDesc">-</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h3>积分信息</h3>
                        <div class="info-item">
                            <span class="info-label">当前积分</span>
                            <span class="info-value points-highlight" id="points">0</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h3>注册信息</h3>
                        <div class="info-item">
                            <span class="info-label">注册时间</span>
                            <span class="info-value" id="createTime">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最后更新</span>
                            <span class="info-value" id="updateTime">-</span>
                        </div>
                    </div>
                </div>
                <button class="btn" onclick="refreshUserInfo()">刷新信息</button>
            </div>

            <!-- 修改密码 -->
            <div id="password-tab" class="tab-content">
                <form id="passwordForm" style="max-width: 500px;">
                    <div class="form-group">
                        <label for="currentPassword">当前密码</label>
                        <input type="password" id="currentPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码</label>
                        <input type="password" id="newPassword" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码</label>
                        <input type="password" id="confirmPassword" required>
                    </div>
                    <button type="submit" class="btn">修改密码</button>
                </form>
            </div>

            <!-- 积分充值 -->
            <div id="recharge-tab" class="tab-content">
                <form id="rechargeForm" style="max-width: 500px;">
                    <div class="form-group">
                        <label for="rechargeCode">充值码</label>
                        <input type="text" id="rechargeCode" placeholder="请输入充值码" required>
                    </div>
                    <button type="submit" class="btn">立即充值</button>
                </form>
                
                <div style="margin-top: 30px; padding: 20px; background: #f8f9ff; border-radius: 10px;">
                    <h3>积分使用说明</h3>
                    <ul style="margin-top: 15px; padding-left: 20px;">
                        <li>面试功能：每次启动消耗 10 积分</li>
                        <li>语音识别：每次处理消耗 5 积分</li>
                        <li>AI答复：每次使用消耗 3 积分</li>
                    </ul>
                </div>
            </div>

            <!-- 充值记录 -->
            <div id="recharge-records-tab" class="tab-content">
                <div class="loading" id="rechargeLoading">加载中...</div>
                <table class="table" id="rechargeTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>充值时间</th>
                            <th>充值类型</th>
                            <th>充值码</th>
                            <th>充值积分</th>
                            <th>充值前</th>
                            <th>充值后</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="rechargeTableBody">
                    </tbody>
                </table>
                <div class="pagination" id="rechargePagination"></div>
            </div>

            <!-- 消费记录 -->
            <div id="consumption-records-tab" class="tab-content">
                <div class="loading" id="consumptionLoading">加载中...</div>
                <table class="table" id="consumptionTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>消费时间</th>
                            <th>消费类型</th>
                            <th>消费积分</th>
                            <th>消费前</th>
                            <th>消费后</th>
                            <th>业务描述</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="consumptionTableBody">
                    </tbody>
                </table>
                <div class="pagination" id="consumptionPagination"></div>
            </div>
        </div>
    </div>

    <script>
        // 当前激活的标签页
        let currentTab = 'info';
        
        // 分页信息
        let rechargeCurrentPage = 1;
        let consumptionCurrentPage = 1;
        const pageSize = 10;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            
            // 绑定表单提交事件
            document.getElementById('passwordForm').addEventListener('submit', handlePasswordChange);
            document.getElementById('rechargeForm').addEventListener('submit', handleRecharge);
        });

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
            
            currentTab = tabName;
            
            // 根据标签页加载相应数据
            if (tabName === 'recharge-records') {
                loadRechargeRecords(1);
            } else if (tabName === 'consumption-records') {
                loadConsumptionRecords(1);
            }
        }

        // 加载用户信息
        async function loadUserInfo() {
            try {
                const response = await fetch('/api/user-center/info', {
                    headers: {
                        'Authorization': 'Bearer ' + getToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    document.getElementById('username').textContent = data.username || '-';
                    document.getElementById('email').textContent = data.email || '-';
                    document.getElementById('phone').textContent = data.phone || '-';
                    document.getElementById('realName').textContent = data.realName || '-';
                    document.getElementById('statusDesc').textContent = data.statusDesc || '-';
                    document.getElementById('points').textContent = data.points || 0;
                    document.getElementById('createTime').textContent = formatDateTime(data.createTime);
                    document.getElementById('updateTime').textContent = formatDateTime(data.updateTime);
                } else {
                    showMessage('获取用户信息失败：' + result.message, 'error');
                }
            } catch (error) {
                showMessage('获取用户信息失败：' + error.message, 'error');
            }
        }

        // 刷新用户信息
        function refreshUserInfo() {
            loadUserInfo();
            showMessage('用户信息已刷新', 'success');
        }

        // 处理密码修改
        async function handlePasswordChange(event) {
            event.preventDefault();
            
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                showMessage('新密码与确认密码不一致', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/user-center/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + getToken()
                    },
                    body: JSON.stringify({
                        currentPassword,
                        newPassword,
                        confirmPassword
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('密码修改成功', 'success');
                    document.getElementById('passwordForm').reset();
                } else {
                    showMessage('密码修改失败：' + result.message, 'error');
                }
            } catch (error) {
                showMessage('密码修改失败：' + error.message, 'error');
            }
        }

        // 处理充值
        async function handleRecharge(event) {
            event.preventDefault();
            
            const rechargeCode = document.getElementById('rechargeCode').value;
            
            try {
                const response = await fetch('/api/user-center/recharge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + getToken()
                    },
                    body: JSON.stringify({
                        rechargeCode
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('充值成功！获得 ' + result.data.pointsAmount + ' 积分', 'success');
                    document.getElementById('rechargeForm').reset();
                    loadUserInfo(); // 刷新用户信息
                } else {
                    showMessage('充值失败：' + result.message, 'error');
                }
            } catch (error) {
                showMessage('充值失败：' + error.message, 'error');
            }
        }

        // 加载充值记录
        async function loadRechargeRecords(page) {
            document.getElementById('rechargeLoading').style.display = 'block';
            document.getElementById('rechargeTable').style.display = 'none';
            
            try {
                const response = await fetch(`/api/user-center/recharge-records?current=${page}&size=${pageSize}`, {
                    headers: {
                        'Authorization': 'Bearer ' + getToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    renderRechargeTable(data.records);
                    renderPagination('rechargePagination', data.current, data.pages, loadRechargeRecords);
                    rechargeCurrentPage = page;
                } else {
                    showMessage('获取充值记录失败：' + result.message, 'error');
                }
            } catch (error) {
                showMessage('获取充值记录失败：' + error.message, 'error');
            } finally {
                document.getElementById('rechargeLoading').style.display = 'none';
                document.getElementById('rechargeTable').style.display = 'table';
            }
        }

        // 加载消费记录
        async function loadConsumptionRecords(page) {
            document.getElementById('consumptionLoading').style.display = 'block';
            document.getElementById('consumptionTable').style.display = 'none';
            
            try {
                const response = await fetch(`/api/user-center/consumption-records?current=${page}&size=${pageSize}`, {
                    headers: {
                        'Authorization': 'Bearer ' + getToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    renderConsumptionTable(data.records);
                    renderPagination('consumptionPagination', data.current, data.pages, loadConsumptionRecords);
                    consumptionCurrentPage = page;
                } else {
                    showMessage('获取消费记录失败：' + result.message, 'error');
                }
            } catch (error) {
                showMessage('获取消费记录失败：' + error.message, 'error');
            } finally {
                document.getElementById('consumptionLoading').style.display = 'none';
                document.getElementById('consumptionTable').style.display = 'table';
            }
        }

        // 渲染充值记录表格
        function renderRechargeTable(records) {
            const tbody = document.getElementById('rechargeTableBody');
            tbody.innerHTML = '';
            
            records.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDateTime(record.createTime)}</td>
                    <td>${record.rechargeTypeDesc}</td>
                    <td>${record.rechargeCodeMasked || '-'}</td>
                    <td>${record.pointsAmount}</td>
                    <td>${record.pointsBefore}</td>
                    <td>${record.pointsAfter}</td>
                    <td><span class="status-${getStatusClass(record.status)}">${record.statusDesc}</span></td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染消费记录表格
        function renderConsumptionTable(records) {
            const tbody = document.getElementById('consumptionTableBody');
            tbody.innerHTML = '';
            
            records.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDateTime(record.createTime)}</td>
                    <td>${record.consumptionTypeDesc}</td>
                    <td>${record.pointsAmount}</td>
                    <td>${record.pointsBefore}</td>
                    <td>${record.pointsAfter}</td>
                    <td>${record.businessDesc || '-'}</td>
                    <td><span class="status-${getStatusClass(record.status)}">${record.statusDesc}</span></td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染分页
        function renderPagination(containerId, current, total, loadFunction) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            if (total <= 1) return;
            
            // 上一页
            if (current > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.textContent = '上一页';
                prevBtn.onclick = () => loadFunction(current - 1);
                container.appendChild(prevBtn);
            }
            
            // 页码
            for (let i = 1; i <= total; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === current ? 'active' : '';
                pageBtn.onclick = () => loadFunction(i);
                container.appendChild(pageBtn);
            }
            
            // 下一页
            if (current < total) {
                const nextBtn = document.createElement('button');
                nextBtn.textContent = '下一页';
                nextBtn.onclick = () => loadFunction(current + 1);
                container.appendChild(nextBtn);
            }
        }

        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        // 格式化时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '-';
            return new Date(dateTime).toLocaleString('zh-CN');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 1: return 'success';
                case 0: return 'failed';
                case 2: return 'processing';
                default: return '';
            }
        }

        // 获取认证token
        function getToken() {
            return localStorage.getItem('token') || '';
        }
    </script>
</body>
</html>
