# JobPlusV8 测试用例说明

## 📋 测试结构概览

### 测试目录结构
```
src/test/java/cloud/ipanda/jobplusv8/
├── controller/                 # 控制器层测试
│   ├── AuthControllerTest.java        # 用户认证控制器测试
│   ├── UserControllerTest.java        # 用户管理控制器测试
│   └── EmailControllerTest.java       # 邮箱验证码控制器测试
├── service/                    # 服务层测试
│   ├── UserServiceTest.java           # 用户服务测试
│   ├── EmailVerificationServiceTest.java  # 邮箱验证码服务测试
│   └── AuditLogServiceTest.java       # 审计日志服务测试
├── util/                       # 工具类测试
│   └── IpUtilTest.java               # IP工具类测试
├── integration/                # 集成测试
│   └── UserIntegrationTest.java      # 用户功能集成测试
└── TestSuite.java             # 测试套件
```

## 🎯 测试用例统计

### 总体统计
- **总测试用例数**: 80+
- **控制器测试**: 30个用例
- **服务层测试**: 35个用例
- **工具类测试**: 24个用例
- **集成测试**: 9个用例

### 详细分布

| 测试类 | 用例数 | 覆盖功能 |
|--------|--------|----------|
| AuthControllerTest | 10 | 登录、获取用户信息、登出 |
| UserControllerTest | 13 | 注册、查询、编辑、删除用户 |
| EmailControllerTest | 12 | 发送验证码、验证验证码 |
| UserServiceTest | 18 | 用户业务逻辑 |
| EmailVerificationServiceTest | 15 | 验证码业务逻辑 |
| AuditLogServiceTest | 10 | 审计日志记录 |
| IpUtilTest | 24 | IP地址解析、浏览器识别 |
| UserIntegrationTest | 9 | 端到端业务流程 |

## 🚀 运行测试

### 1. 运行所有测试
```bash
# Maven
mvn test

# Gradle
./gradlew test

# IDE中运行TestSuite类
```

### 2. 运行特定测试类
```bash
# 运行用户认证测试
mvn test -Dtest=AuthControllerTest

# 运行用户服务测试
mvn test -Dtest=UserServiceTest

# 运行集成测试
mvn test -Dtest=UserIntegrationTest
```

### 3. 运行特定测试方法
```bash
# 运行特定测试方法
mvn test -Dtest=AuthControllerTest#testLoginSuccess
```

### 4. 生成测试报告
```bash
# 生成测试覆盖率报告
mvn jacoco:report

# 查看报告
open target/site/jacoco/index.html
```

## 🔧 测试环境配置

### 测试数据库
- **类型**: H2内存数据库
- **配置**: `application-test.yml`
- **特点**: 每次测试后自动清理

### 测试Redis
- **配置**: 使用测试专用数据库
- **端口**: 6379 (database: 1)
- **清理**: 测试后自动清理

### 邮件服务
- **模式**: Mock模式
- **配置**: 不实际发送邮件
- **验证**: 通过Mock验证调用

## 📊 测试覆盖范围

### 功能覆盖
- ✅ **用户认证**: 登录、登出、Token验证
- ✅ **用户管理**: 注册、查询、编辑、删除
- ✅ **邮箱验证码**: 发送、验证、频率限制
- ✅ **审计日志**: 操作记录、异步处理
- ✅ **工具类**: IP解析、浏览器识别
- ✅ **集成流程**: 端到端业务流程

### 异常覆盖
- ✅ **参数验证**: 空值、格式错误
- ✅ **业务异常**: 用户不存在、密码错误
- ✅ **系统异常**: 数据库错误、网络异常
- ✅ **权限控制**: 未登录、权限不足

### 边界条件
- ✅ **数据边界**: 最大长度、最小长度
- ✅ **时间边界**: 过期时间、频率限制
- ✅ **并发处理**: 多用户同时操作

## 🎨 测试最佳实践

### 1. 测试命名规范
```java
@Test
@DisplayName("AUTH-001: 正常登录")
void testLoginSuccess() {
    // 测试编号 + 功能描述
}
```

### 2. 测试结构 (AAA模式)
```java
@Test
void testExample() {
    // Arrange - 准备测试数据
    User user = new User();
    user.setUsername("test");
    
    // Act - 执行测试操作
    boolean result = userService.register(user);
    
    // Assert - 验证测试结果
    assertTrue(result);
    verify(userMapper).insert(any(User.class));
}
```

### 3. Mock使用原则
- **外部依赖**: 数据库、Redis、邮件服务
- **复杂逻辑**: 第三方服务、网络调用
- **隔离测试**: 只测试当前类的逻辑

### 4. 测试数据管理
- **独立性**: 每个测试用例独立
- **清理**: 测试后清理数据
- **隔离**: 避免测试间相互影响

## 🔍 测试用例详解

### 控制器测试
```java
@WebMvcTest(AuthController.class)  // 只加载Web层
@ActiveProfiles("test")            // 使用测试配置
class AuthControllerTest {
    
    @Autowired
    private MockMvc mockMvc;       // 模拟HTTP请求
    
    @MockBean
    private UserService userService;  // Mock服务层
    
    @Test
    void testLogin() throws Exception {
        // 使用MockMvc测试HTTP接口
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
```

### 服务层测试
```java
@ExtendWith(MockitoExtension.class)  // 使用Mockito
class UserServiceTest {
    
    @Mock
    private UserMapper userMapper;      // Mock数据层
    
    @InjectMocks
    private UserServiceImpl userService; // 注入被测试类
    
    @Test
    void testRegister() {
        // 纯业务逻辑测试，不依赖外部服务
        when(userMapper.insert(any())).thenReturn(1);
        
        boolean result = userService.register(user);
        
        assertTrue(result);
        verify(userMapper).insert(any(User.class));
    }
}
```

### 集成测试
```java
@SpringBootTest                    // 完整Spring上下文
@Transactional                     // 事务回滚
class UserIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;       // 真实的Web层
    
    @Autowired
    private UserService userService;  // 真实的服务层
    
    @Test
    void testCompleteFlow() {
        // 测试完整的业务流程
        // 1. 发送验证码
        // 2. 用户注册
        // 3. 用户登录
        // 4. 获取用户信息
    }
}
```

## 🐛 常见问题解决

### 1. 测试数据库连接失败
```yaml
# 检查 application-test.yml 配置
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
```

### 2. Mock对象注入失败
```java
// 确保使用正确的注解
@MockBean  // Spring Boot测试中使用
@Mock      // 纯Mockito测试中使用
```

### 3. 测试事务不回滚
```java
// 添加事务注解
@Transactional
@Rollback  // 确保回滚
```

### 4. 异步方法测试
```java
// 等待异步方法完成
@Test
void testAsync() throws Exception {
    Future<Void> future = asyncService.doSomething();
    future.get(5, TimeUnit.SECONDS);  // 等待完成
}
```

## 📈 测试报告

### 覆盖率目标
- **行覆盖率**: > 80%
- **分支覆盖率**: > 70%
- **方法覆盖率**: > 90%

### 质量指标
- **测试通过率**: 100%
- **测试执行时间**: < 2分钟
- **测试稳定性**: 无随机失败

## 🔄 持续集成

### CI/CD配置
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: '11'
      - name: Run tests
        run: mvn test
      - name: Generate test report
        run: mvn jacoco:report
```

---

**测试文档版本**: v1.0.0  
**更新时间**: 2025年7月22日  
**维护团队**: JobPlusV8开发团队
