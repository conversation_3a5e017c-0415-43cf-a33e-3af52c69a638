package cloud.ipanda.jobplusv8;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 应用启动测试
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("应用启动测试")
class Jobplusv8ApplicationTests {

    @Test
    @DisplayName("应用上下文加载测试")
    void contextLoads() {
        // 这个测试验证Spring应用上下文能够正常加载
        // 如果上下文加载失败，测试会自动失败
    }
}
