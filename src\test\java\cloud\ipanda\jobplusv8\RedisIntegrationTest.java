package cloud.ipanda.jobplusv8;

import cloud.ipanda.jobplusv8.service.EmailVerificationService;
import cloud.ipanda.jobplusv8.util.RedisUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis集成测试
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("Redis集成测试")
public class RedisIntegrationTest {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private EmailVerificationService emailVerificationService;

    @Test
    public void testRedisConnection() {
        // 测试Redis连接和基本操作
        String testKey = "test:redis:connection";
        String testValue = "Hello Redis!";

        // 测试设置值
        boolean setResult = redisUtil.setString(testKey, testValue, 60, TimeUnit.SECONDS);
        assertTrue(setResult, "Redis设置值应该成功");

        // 测试获取值
        String getValue = redisUtil.getString(testKey);
        assertEquals(testValue, getValue, "Redis获取的值应该与设置的值相同");

        // 测试键是否存在
        boolean exists = redisUtil.hasKey(testKey);
        assertTrue(exists, "Redis键应该存在");

        // 测试删除键
        boolean deleteResult = redisUtil.deleteString(testKey);
        assertTrue(deleteResult, "Redis删除键应该成功");

        // 验证键已被删除
        boolean existsAfterDelete = redisUtil.hasKey(testKey);
        assertFalse(existsAfterDelete, "删除后Redis键不应该存在");
    }

    @Test
    public void testRedisExpiration() throws InterruptedException {
        // 测试Redis过期功能
        String testKey = "test:redis:expiration";
        String testValue = "This will expire";

        // 设置2秒过期时间
        boolean setResult = redisUtil.setString(testKey, testValue, 2, TimeUnit.SECONDS);
        assertTrue(setResult, "Redis设置值应该成功");

        // 立即获取值，应该存在
        String getValue = redisUtil.getString(testKey);
        assertEquals(testValue, getValue, "Redis获取的值应该与设置的值相同");

        // 等待3秒
        Thread.sleep(3000);

        // 再次获取值，应该已过期
        String expiredValue = redisUtil.getString(testKey);
        assertNull(expiredValue, "过期后Redis值应该为null");
    }

    @Test
    public void testEmailVerificationService() {
        // 测试邮箱验证码服务
        String testEmail = "<EMAIL>";
        String testType = "register";

        // 测试发送间隔检查
        boolean canSend = emailVerificationService.canSendCode(testEmail, testType);
        assertTrue(canSend, "初始状态应该可以发送验证码");

        // 注意：这个测试可能会实际发送邮件，在生产环境中需要谨慎
        // 如果不想实际发送邮件，可以注释掉下面的代码
        /*
        boolean sendResult = emailVerificationService.sendVerificationCode(testEmail, testType);
        assertTrue(sendResult, "验证码发送应该成功");

        // 发送后应该不能立即再次发送
        boolean canSendAgain = emailVerificationService.canSendCode(testEmail, testType);
        assertFalse(canSendAgain, "发送后应该不能立即再次发送");

        // 获取剩余等待时间
        long remainingTime = emailVerificationService.getRemainingWaitTime(testEmail, testType);
        assertTrue(remainingTime > 0, "剩余等待时间应该大于0");
        */
    }

    @Test
    public void testRedisIncrement() {
        // 测试Redis递增操作
        String testKey = "test:redis:counter";

        // 删除可能存在的键
        redisUtil.deleteString(testKey);

        // 测试递增
        long result1 = redisUtil.increment(testKey, 1);
        assertEquals(1, result1, "第一次递增结果应该是1");

        long result2 = redisUtil.increment(testKey, 5);
        assertEquals(6, result2, "第二次递增结果应该是6");

        // 测试递减
        long result3 = redisUtil.decrement(testKey, 2);
        assertEquals(4, result3, "递减结果应该是4");

        // 清理测试数据
        redisUtil.deleteString(testKey);
    }

    @Test
    public void testRedisObjectSerialization() {
        // 测试Redis对象序列化
        String testKey = "test:redis:object";
        
        // 创建测试对象
        TestObject testObject = new TestObject();
        testObject.setId(1L);
        testObject.setName("Test Object");
        testObject.setActive(true);

        // 设置对象
        boolean setResult = redisUtil.set(testKey, testObject, 60, TimeUnit.SECONDS);
        assertTrue(setResult, "Redis设置对象应该成功");

        // 获取对象
        Object getValue = redisUtil.get(testKey);
        assertNotNull(getValue, "Redis获取的对象不应该为null");

        // 清理测试数据
        redisUtil.delete(testKey);
    }

    // 测试用的简单对象
    public static class TestObject {
        private Long id;
        private String name;
        private Boolean active;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Boolean getActive() { return active; }
        public void setActive(Boolean active) { this.active = active; }
    }
}
