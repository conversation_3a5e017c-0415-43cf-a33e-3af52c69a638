package cloud.ipanda.jobplusv8.mail;


import cloud.ipanda.jobplusv8.entity.VerificationCode;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/21 13:38
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MailServiceTest {

    @Autowired
    private TencentMailService mailService;


    @Test
    public void testSend() {

        //mailService.sendSimpleMail("<EMAIL>", "test", "test");

        mailService.sendVerificationCode("<EMAIL>", VerificationCode.Purpose.REGISTER);
    }
}
