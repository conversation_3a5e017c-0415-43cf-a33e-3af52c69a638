package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.service.impl.EmailVerificationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;

import javax.mail.internet.MimeMessage;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 邮箱验证码服务测试
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
@DisplayName("邮箱验证码服务测试")
class EmailVerificationServiceTest {

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @Mock
    private JavaMailSender mailSender;

    @Mock
    private MimeMessage mimeMessage;

    @InjectMocks
    private EmailVerificationServiceImpl emailVerificationService;

    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        
        // 设置配置属性
        ReflectionTestUtils.setField(emailVerificationService, "fromEmail", "<EMAIL>");
        ReflectionTestUtils.setField(emailVerificationService, "fromName", "JobPlusV8测试系统");
        ReflectionTestUtils.setField(emailVerificationService, "codeExpireMinutes", 5);
        ReflectionTestUtils.setField(emailVerificationService, "codeLength", 6);
        ReflectionTestUtils.setField(emailVerificationService, "sendIntervalSeconds", 60);
    }

    @Test
    @DisplayName("EMAIL-SERVICE-001: 正常发送验证码")
    void testSendVerificationCodeSuccess() {
        // Given
        String email = "<EMAIL>";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenReturn(null); // 没有发送记录
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailVerificationService.sendVerificationCode(email, type);

        // Then
        assertTrue(result);
        verify(valueOperations, times(2)).set(anyString(), anyString(), anyLong(), eq(TimeUnit.SECONDS));
        verify(mailSender).send(any(MimeMessage.class));
    }

    @Test
    @DisplayName("EMAIL-SERVICE-002: 发送过于频繁")
    void testSendVerificationCodeTooFrequent() {
        // Given
        String email = "<EMAIL>";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenReturn("sent"); // 已有发送记录

        // When
        boolean result = emailVerificationService.sendVerificationCode(email, type);

        // Then
        assertFalse(result);
        verify(mailSender, never()).send(any(MimeMessage.class));
    }

    @Test
    @DisplayName("EMAIL-SERVICE-003: 邮件发送异常")
    void testSendVerificationCodeWithMailException() {
        // Given
        String email = "<EMAIL>";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenReturn(null);
        doThrow(new RuntimeException("Mail server error")).when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailVerificationService.sendVerificationCode(email, type);

        // Then
        assertFalse(result);
        verify(mailSender).send(any(MimeMessage.class));
    }

    @Test
    @DisplayName("EMAIL-SERVICE-004: 正常验证验证码")
    void testVerifyCodeSuccess() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenReturn("123456");

        // When
        boolean result = emailVerificationService.verifyCode(email, code, type);

        // Then
        assertTrue(result);
        verify(valueOperations).get(anyString());
    }

    @Test
    @DisplayName("EMAIL-SERVICE-005: 验证码错误")
    void testVerifyCodeFailure() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenReturn("654321"); // 不同的验证码

        // When
        boolean result = emailVerificationService.verifyCode(email, code, type);

        // Then
        assertFalse(result);
        verify(valueOperations).get(anyString());
    }

    @Test
    @DisplayName("EMAIL-SERVICE-006: 验证码不存在或已过期")
    void testVerifyCodeNotExists() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenReturn(null);

        // When
        boolean result = emailVerificationService.verifyCode(email, code, type);

        // Then
        assertFalse(result);
        verify(valueOperations).get(anyString());
    }

    @Test
    @DisplayName("EMAIL-SERVICE-007: 检查是否可以发送验证码")
    void testCanSendCode() {
        // Given
        String email = "<EMAIL>";
        String type = "register";

        // 测试可以发送的情况
        when(valueOperations.get(anyString())).thenReturn(null);
        assertTrue(emailVerificationService.canSendCode(email, type));

        // 测试不能发送的情况
        when(valueOperations.get(anyString())).thenReturn("sent");
        assertFalse(emailVerificationService.canSendCode(email, type));
    }

    @Test
    @DisplayName("EMAIL-SERVICE-008: 获取剩余等待时间")
    void testGetRemainingWaitTime() {
        // Given
        String email = "<EMAIL>";
        String type = "register";
        
        when(redisTemplate.getExpire(anyString(), eq(TimeUnit.SECONDS))).thenReturn(45L);

        // When
        long remainingTime = emailVerificationService.getRemainingWaitTime(email, type);

        // Then
        assertEquals(45L, remainingTime);
        verify(redisTemplate).getExpire(anyString(), eq(TimeUnit.SECONDS));
    }

    @Test
    @DisplayName("EMAIL-SERVICE-009: 获取剩余等待时间-无记录")
    void testGetRemainingWaitTimeNoRecord() {
        // Given
        String email = "<EMAIL>";
        String type = "register";
        
        when(redisTemplate.getExpire(anyString(), eq(TimeUnit.SECONDS))).thenReturn(-2L); // key不存在

        // When
        long remainingTime = emailVerificationService.getRemainingWaitTime(email, type);

        // Then
        assertEquals(0L, remainingTime);
    }

    @Test
    @DisplayName("EMAIL-SERVICE-010: 生成验证码")
    void testGenerateCode() throws Exception {
        // Given
        Method generateCodeMethod = EmailVerificationServiceImpl.class.getDeclaredMethod("generateCode");
        generateCodeMethod.setAccessible(true);

        // When
        String code = (String) generateCodeMethod.invoke(emailVerificationService);

        // Then
        assertNotNull(code);
        assertEquals(6, code.length());
        assertTrue(code.matches("\\d{6}")); // 6位数字
    }

    @Test
    @DisplayName("EMAIL-SERVICE-011: 获取邮件主题")
    void testGetEmailSubject() throws Exception {
        // Given
        Method getEmailSubjectMethod = EmailVerificationServiceImpl.class.getDeclaredMethod("getEmailSubject", String.class);
        getEmailSubjectMethod.setAccessible(true);

        // When & Then
        String registerSubject = (String) getEmailSubjectMethod.invoke(emailVerificationService, "register");
        assertEquals("【JobPlusV8测试系统】账号注册验证码", registerSubject);

        String loginSubject = (String) getEmailSubjectMethod.invoke(emailVerificationService, "login");
        assertEquals("【JobPlusV8测试系统】账号登录验证码", loginSubject);

        String resetSubject = (String) getEmailSubjectMethod.invoke(emailVerificationService, "reset");
        assertEquals("【JobPlusV8测试系统】密码重置验证码", resetSubject);
    }

    @Test
    @DisplayName("EMAIL-SERVICE-012: 获取操作名称")
    void testGetActionName() throws Exception {
        // Given
        Method getActionNameMethod = EmailVerificationServiceImpl.class.getDeclaredMethod("getActionName", String.class);
        getActionNameMethod.setAccessible(true);

        // When & Then
        String registerAction = (String) getActionNameMethod.invoke(emailVerificationService, "register");
        assertEquals("账号注册", registerAction);

        String loginAction = (String) getActionNameMethod.invoke(emailVerificationService, "login");
        assertEquals("账号登录", loginAction);

        String resetAction = (String) getActionNameMethod.invoke(emailVerificationService, "reset");
        assertEquals("密码重置", resetAction);

        String defaultAction = (String) getActionNameMethod.invoke(emailVerificationService, "unknown");
        assertEquals("身份验证", defaultAction);
    }

    @Test
    @DisplayName("EMAIL-SERVICE-013: 获取操作颜色")
    void testGetActionColor() throws Exception {
        // Given
        Method getActionColorMethod = EmailVerificationServiceImpl.class.getDeclaredMethod("getActionColor", String.class);
        getActionColorMethod.setAccessible(true);

        // When & Then
        String registerColor = (String) getActionColorMethod.invoke(emailVerificationService, "register");
        assertEquals("#28a745", registerColor);

        String loginColor = (String) getActionColorMethod.invoke(emailVerificationService, "login");
        assertEquals("#007bff", loginColor);

        String resetColor = (String) getActionColorMethod.invoke(emailVerificationService, "reset");
        assertEquals("#dc3545", resetColor);

        String defaultColor = (String) getActionColorMethod.invoke(emailVerificationService, "unknown");
        assertEquals("#6c757d", defaultColor);
    }

    @Test
    @DisplayName("EMAIL-SERVICE-014: 不同类型验证码独立存储")
    void testDifferentTypesIndependentStorage() {
        // Given
        String email = "<EMAIL>";
        
        when(valueOperations.get(anyString())).thenReturn(null);
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When - 发送不同类型的验证码
        boolean registerResult = emailVerificationService.sendVerificationCode(email, "register");
        boolean loginResult = emailVerificationService.sendVerificationCode(email, "login");
        boolean resetResult = emailVerificationService.sendVerificationCode(email, "reset");

        // Then
        assertTrue(registerResult);
        assertTrue(loginResult);
        assertTrue(resetResult);
        
        // 验证不同类型的验证码使用不同的Redis key
        verify(valueOperations, times(6)).set(anyString(), anyString(), anyLong(), eq(TimeUnit.SECONDS));
        verify(mailSender, times(3)).send(any(MimeMessage.class));
    }

    @Test
    @DisplayName("EMAIL-SERVICE-015: Redis异常处理")
    void testRedisException() {
        // Given
        String email = "<EMAIL>";
        String type = "register";
        
        when(valueOperations.get(anyString())).thenThrow(new RuntimeException("Redis connection failed"));

        // When
        boolean canSend = emailVerificationService.canSendCode(email, type);
        boolean sendResult = emailVerificationService.sendVerificationCode(email, type);
        boolean verifyResult = emailVerificationService.verifyCode(email, "123456", type);

        // Then
        assertTrue(canSend); // Redis异常时默认允许发送
        assertFalse(sendResult); // 发送失败
        assertFalse(verifyResult); // 验证失败
    }
}
