package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.dto.ResumeResponse;
import cloud.ipanda.jobplusv8.dto.ResumeUploadRequest;
import cloud.ipanda.jobplusv8.entity.Resume;
import cloud.ipanda.jobplusv8.mapper.ResumeMapper;
import cloud.ipanda.jobplusv8.service.impl.ResumeServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 简历服务测试类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
class ResumeServiceTest {

    @Mock
    private ResumeMapper resumeMapper;

    @Mock
    private FileUploadService fileUploadService;

    @Mock
    private GeminiService geminiService;

    @InjectMocks
    private ResumeServiceImpl resumeService;

    private Resume testResume;
    private ResumeUploadRequest uploadRequest;
    private MockMultipartFile testFile;

    @BeforeEach
    void setUp() {
        // 创建测试简历对象
        testResume = new Resume();
        testResume.setId(1L);
        testResume.setUserId(1L);
        testResume.setResumeName("张三-Java开发工程师");
        testResume.setOriginalFilename("张三简历.pdf");
        testResume.setStoredFilename("1_abc123_1704326400000.pdf");
        testResume.setFilePath("./uploads/resumes/2025/01/04/1/1_abc123_1704326400000.pdf");
        testResume.setFileSize(1024000L);
        testResume.setFileType("pdf");
        testResume.setMimeType("application/pdf");
        testResume.setIsDefault(0);
        testResume.setParseStatus(Resume.ParseStatus.NOT_PARSED);
        testResume.setStatus(Resume.Status.ENABLED);

        // 创建测试文件
        testFile = new MockMultipartFile(
            "file",
            "test-resume.pdf",
            "application/pdf",
            "test content".getBytes()
        );

        // 创建上传请求
        uploadRequest = new ResumeUploadRequest();
        uploadRequest.setResumeName("张三-Java开发工程师");
        uploadRequest.setFile(testFile);
        uploadRequest.setIsDefault(false);
    }

    @Test
    void testUploadResume_Success() {
        // 准备测试数据
        Long userId = 1L;
        String filePath = "./uploads/resumes/2025/01/04/1/test.pdf";

        // Mock依赖服务
        when(fileUploadService.uploadResumeFile(any(), eq(userId))).thenReturn(filePath);
        when(fileUploadService.getFileExtension(anyString())).thenReturn("pdf");
        when(fileUploadService.getMimeType(any())).thenReturn("application/pdf");

        // 执行测试
        ResumeResponse response = resumeService.uploadResume(uploadRequest, userId);

        // 验证结果
        assertNotNull(response);
        assertEquals("张三-Java开发工程师", response.getResumeName());
        assertEquals("test-resume.pdf", response.getOriginalFilename());
        assertEquals(false, response.getIsDefault());

        // 验证方法调用
        verify(fileUploadService).uploadResumeFile(testFile, userId);
        verify(resumeMapper, never()).clearDefaultResume(userId); // 非默认简历不应清除其他默认设置
    }

    @Test
    void testUploadResume_SetAsDefault() {
        // 准备测试数据
        Long userId = 1L;
        String filePath = "./uploads/resumes/2025/01/04/1/test.pdf";
        uploadRequest.setIsDefault(true);

        // Mock依赖服务
        when(fileUploadService.uploadResumeFile(any(), eq(userId))).thenReturn(filePath);
        when(fileUploadService.getFileExtension(anyString())).thenReturn("pdf");
        when(fileUploadService.getMimeType(any())).thenReturn("application/pdf");
        when(resumeMapper.clearDefaultResume(userId)).thenReturn(1);

        // 执行测试
        ResumeResponse response = resumeService.uploadResume(uploadRequest, userId);

        // 验证结果
        assertNotNull(response);
        assertEquals(true, response.getIsDefault());

        // 验证方法调用
        verify(resumeMapper).clearDefaultResume(userId); // 设为默认时应清除其他默认设置
    }

    @Test
    void testGetUserResumes() {
        // 准备测试数据
        Long userId = 1L;
        Integer current = 1;
        Integer size = 10;

        List<Resume> resumeList = Arrays.asList(testResume);
        Page<Resume> resumePage = new Page<>(current, size);
        resumePage.setRecords(resumeList);
        resumePage.setTotal(1);

        // Mock数据库查询
        when(resumeService.page(any(Page.class), any())).thenReturn(resumePage);

        // 执行测试
        Page<ResumeResponse> result = resumeService.getUserResumes(userId, current, size);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        
        ResumeResponse response = result.getRecords().get(0);
        assertEquals("张三-Java开发工程师", response.getResumeName());
        assertEquals("张三简历.pdf", response.getOriginalFilename());
    }

    @Test
    void testDeleteResume_Success() {
        // 准备测试数据
        Long resumeId = 1L;
        Long userId = 1L;

        // Mock数据库查询
        when(resumeService.getById(resumeId)).thenReturn(testResume);
        when(resumeService.removeById(resumeId)).thenReturn(true);
        when(fileUploadService.deleteFile(testResume.getFilePath())).thenReturn(true);

        // 执行测试
        boolean result = resumeService.deleteResume(resumeId, userId);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(fileUploadService).deleteFile(testResume.getFilePath());
        verify(resumeService).removeById(resumeId);
    }

    @Test
    void testDeleteResume_UnauthorizedUser() {
        // 准备测试数据
        Long resumeId = 1L;
        Long userId = 2L; // 不同的用户ID

        // Mock数据库查询
        when(resumeService.getById(resumeId)).thenReturn(testResume);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            resumeService.deleteResume(resumeId, userId);
        });

        assertEquals("简历不存在或无权限访问", exception.getMessage());

        // 验证不应调用删除方法
        verify(fileUploadService, never()).deleteFile(anyString());
        verify(resumeService, never()).removeById(anyLong());
    }

    @Test
    void testSetDefaultResume() {
        // 准备测试数据
        Long resumeId = 1L;
        Long userId = 1L;

        // Mock数据库操作
        when(resumeService.getById(resumeId)).thenReturn(testResume);
        when(resumeMapper.clearDefaultResume(userId)).thenReturn(1);
        when(resumeService.updateById(any(Resume.class))).thenReturn(true);

        // 执行测试
        boolean result = resumeService.setDefaultResume(resumeId, userId);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(resumeMapper).clearDefaultResume(userId);
        verify(resumeService).updateById(any(Resume.class));
    }

    @Test
    void testConvertToResponse() {
        // 执行测试
        ResumeResponse response = resumeService.convertToResponse(testResume);

        // 验证结果
        assertNotNull(response);
        assertEquals(testResume.getId(), response.getId());
        assertEquals(testResume.getResumeName(), response.getResumeName());
        assertEquals(testResume.getOriginalFilename(), response.getOriginalFilename());
        assertEquals(testResume.getFileSize(), response.getFileSize());
        assertEquals(testResume.getFileType(), response.getFileType());
        assertEquals(false, response.getIsDefault()); // is_default = 0 转换为 false
        assertEquals(testResume.getParseStatus(), response.getParseStatus());

        // 验证文件大小格式化
        assertNotNull(response.getFileSizeFormatted());
        assertTrue(response.getFileSizeFormatted().contains("KB") || 
                  response.getFileSizeFormatted().contains("MB"));

        // 验证解析状态描述
        assertNotNull(response.getParseStatusDesc());
        assertEquals("未解析", response.getParseStatusDesc());
    }

    @Test
    void testGetDefaultResume() {
        // 准备测试数据
        Long userId = 1L;
        testResume.setIsDefault(1); // 设为默认简历

        // Mock数据库查询
        when(resumeService.getOne(any())).thenReturn(testResume);

        // 执行测试
        ResumeResponse result = resumeService.getDefaultResume(userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(true, result.getIsDefault());
        assertEquals("张三-Java开发工程师", result.getResumeName());
    }

    @Test
    void testGetDefaultResume_NotFound() {
        // 准备测试数据
        Long userId = 1L;

        // Mock数据库查询返回null
        when(resumeService.getOne(any())).thenReturn(null);

        // 执行测试
        ResumeResponse result = resumeService.getDefaultResume(userId);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testReparseResume() {
        // 准备测试数据
        Long resumeId = 1L;
        Long userId = 1L;

        // Mock数据库操作
        when(resumeService.getById(resumeId)).thenReturn(testResume);
        when(resumeService.updateById(any(Resume.class))).thenReturn(true);

        // 执行测试
        boolean result = resumeService.reparseResume(resumeId, userId);

        // 验证结果
        assertTrue(result);

        // 验证简历状态被重置
        verify(resumeService).updateById(argThat(resume -> 
            resume.getParseStatus() == Resume.ParseStatus.NOT_PARSED &&
            resume.getParsedContent() == null &&
            resume.getParseError() == null &&
            resume.getParsedAt() == null
        ));
    }
}
