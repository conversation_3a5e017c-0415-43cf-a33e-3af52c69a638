package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.dto.*;
import cloud.ipanda.jobplusv8.entity.RechargeCode;
import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.mapper.UserMapper;
import cloud.ipanda.jobplusv8.service.impl.UserCenterServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户中心服务测试类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
class UserCenterServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private PointsService pointsService;

    @Mock
    private RechargeCodeService rechargeCodeService;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private UserCenterServiceImpl userCenterService;

    private User testUser;
    private RechargeCode testRechargeCode;

    @BeforeEach
    void setUp() {
        // 准备测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("13800138000");
        testUser.setRealName("测试用户");
        testUser.setStatus(1);
        testUser.setPoints(100L);
        testUser.setCreateTime(LocalDateTime.now().minusDays(30));
        testUser.setUpdateTime(LocalDateTime.now());

        // 准备测试充值码
        testRechargeCode = new RechargeCode();
        testRechargeCode.setId(1L);
        testRechargeCode.setCode("encrypted_code");
        testRechargeCode.setOriginalCode("RC20250804ABCD1234");
        testRechargeCode.setPointsAmount(1000L);
        testRechargeCode.setAmount(new BigDecimal("10.00"));
        testRechargeCode.setStatus(RechargeCode.Status.UNUSED);
    }

    @Test
    void testGetUserCenterInfo_Success() {
        // 准备测试数据
        when(userMapper.selectById(1L)).thenReturn(testUser);

        // 执行测试
        UserCenterResponse response = userCenterService.getUserCenterInfo(1L);

        // 验证结果
        assertNotNull(response);
        assertEquals("testuser", response.getUsername());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals(Long.valueOf(100), response.getPoints());
        assertEquals("正常", response.getStatusDesc());

        verify(userMapper).selectById(1L);
    }

    @Test
    void testGetUserCenterInfo_UserNotFound() {
        // 准备测试数据
        when(userMapper.selectById(1L)).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userCenterService.getUserCenterInfo(1L);
        });

        assertEquals("用户不存在", exception.getMessage());
        verify(userMapper).selectById(1L);
    }

    @Test
    void testChangePassword_Success() {
        // 准备测试数据
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("newPassword");

        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(passwordEncoder.matches("oldPassword", testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.encode("newPassword")).thenReturn("encodedNewPassword");
        when(userMapper.updateById(any(User.class))).thenReturn(1);

        // 执行测试
        boolean result = userCenterService.changePassword(1L, request);

        // 验证结果
        assertTrue(result);
        verify(userMapper).selectById(1L);
        verify(passwordEncoder).matches("oldPassword", testUser.getPassword());
        verify(passwordEncoder).encode("newPassword");
        verify(userMapper).updateById(any(User.class));
    }

    @Test
    void testChangePassword_PasswordMismatch() {
        // 准备测试数据
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("differentPassword");

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userCenterService.changePassword(1L, request);
        });

        assertEquals("新密码与确认密码不一致", exception.getMessage());
    }

    @Test
    void testChangePassword_WrongCurrentPassword() {
        // 准备测试数据
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("wrongPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("newPassword");

        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(passwordEncoder.matches("wrongPassword", testUser.getPassword())).thenReturn(false);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userCenterService.changePassword(1L, request);
        });

        assertEquals("当前密码错误", exception.getMessage());
        verify(userMapper).selectById(1L);
        verify(passwordEncoder).matches("wrongPassword", testUser.getPassword());
    }

    @Test
    void testRechargeByCode_Success() {
        // 准备测试数据
        RechargeRequest request = new RechargeRequest();
        request.setRechargeCode("RC20250804ABCD1234");

        when(rechargeCodeService.validateRechargeCode("RC20250804ABCD1234")).thenReturn(testRechargeCode);
        when(pointsService.getUserPoints(1L)).thenReturn(100L);
        when(rechargeCodeService.useRechargeCode("RC20250804ABCD1234", 1L)).thenReturn(true);
        when(pointsService.addUserPoints(1L, 1000L)).thenReturn(true);

        // 执行测试
        PointsRechargeResponse response = userCenterService.rechargeByCode(1L, request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Long.valueOf(1000), response.getPointsAmount());
        assertEquals(Long.valueOf(100), response.getPointsBefore());
        assertEquals(Long.valueOf(1100), response.getPointsAfter());

        verify(rechargeCodeService).validateRechargeCode("RC20250804ABCD1234");
        verify(pointsService).getUserPoints(1L);
        verify(rechargeCodeService).useRechargeCode("RC20250804ABCD1234", 1L);
        verify(pointsService).addUserPoints(1L, 1000L);
    }

    @Test
    void testRechargeByCode_InvalidCode() {
        // 准备测试数据
        RechargeRequest request = new RechargeRequest();
        request.setRechargeCode("INVALID_CODE");

        when(rechargeCodeService.validateRechargeCode("INVALID_CODE")).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userCenterService.rechargeByCode(1L, request);
        });

        assertEquals("充值码无效或已过期", exception.getMessage());
        verify(rechargeCodeService).validateRechargeCode("INVALID_CODE");
    }

    @Test
    void testAdminRecharge_Success() {
        // 准备测试数据
        AdminRechargeRequest request = new AdminRechargeRequest();
        request.setUserId(2L);
        request.setPointsAmount(500L);
        request.setRemark("管理员测试充值");

        User targetUser = new User();
        targetUser.setId(2L);
        targetUser.setUsername("targetuser");

        when(userMapper.selectById(2L)).thenReturn(targetUser);
        when(pointsService.getUserPoints(2L)).thenReturn(200L);
        when(pointsService.addUserPoints(2L, 500L)).thenReturn(true);

        // 执行测试
        PointsRechargeResponse response = userCenterService.adminRecharge(1L, "admin", request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Long.valueOf(500), response.getPointsAmount());
        assertEquals(Long.valueOf(200), response.getPointsBefore());
        assertEquals(Long.valueOf(700), response.getPointsAfter());
        assertEquals("admin", response.getOperatorUsername());

        verify(userMapper).selectById(2L);
        verify(pointsService).getUserPoints(2L);
        verify(pointsService).addUserPoints(2L, 500L);
    }

    @Test
    void testAdminRecharge_TargetUserNotFound() {
        // 准备测试数据
        AdminRechargeRequest request = new AdminRechargeRequest();
        request.setUserId(999L);
        request.setPointsAmount(500L);

        when(userMapper.selectById(999L)).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userCenterService.adminRecharge(1L, "admin", request);
        });

        assertEquals("目标用户不存在", exception.getMessage());
        verify(userMapper).selectById(999L);
    }

    @Test
    void testGetRechargeRecords() {
        // 准备测试数据
        Page<PointsRechargeResponse> expectedPage = new Page<>();
        // 这里可以添加更多的模拟数据

        // 执行测试
        Page<PointsRechargeResponse> result = userCenterService.getRechargeRecords(1L, 1, 10);

        // 验证结果
        assertNotNull(result);
        // 可以添加更多的验证逻辑
    }

    @Test
    void testGetConsumptionRecords() {
        // 准备测试数据
        Page<PointsConsumptionResponse> expectedPage = new Page<>();
        // 这里可以添加更多的模拟数据

        // 执行测试
        Page<PointsConsumptionResponse> result = userCenterService.getConsumptionRecords(1L, 1, 10);

        // 验证结果
        assertNotNull(result);
        // 可以添加更多的验证逻辑
    }
}
