package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户服务测试
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@Transactional
@DisplayName("用户服务测试")
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    @DisplayName("SERVICE-001: 正常注册用户")
    void testRegisterSuccess() {
        // Given
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("test");
        newUser.setPassword("123456");
        newUser.setEmail("testuser_" + timestamp + "@example.com");
        newUser.setRealName("测试用户");

        // When
        boolean result = userService.register(newUser);

        // Then
        assertTrue(result);
        assertEquals(1, newUser.getStatus());
        assertNotNull(newUser.getCreateTime());
        assertNotNull(newUser.getUpdateTime());
        
        // 验证密码已加密（不等于原始密码）
        assertNotEquals("123456", newUser.getPassword());
        
        // 验证用户已保存到数据库
        User savedUser = userService.getByUsername(newUser.getUsername());
        assertNotNull(savedUser);
        assertEquals(newUser.getEmail(), savedUser.getEmail());
        assertEquals(newUser.getRealName(), savedUser.getRealName());
    }

    @Test
    @DisplayName("SERVICE-002: 注册失败-用户名已存在")
    void testRegisterFailureUsernameExists() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User existingUser = new User();
        existingUser.setUsername("existing_" + timestamp);
        existingUser.setPassword("123456");
        existingUser.setEmail("existing_" + timestamp + "@example.com");
        existingUser.setRealName("已存在用户");
        
        // 先注册一个用户
        userService.register(existingUser);
        
        // 尝试注册相同用户名的用户
        User newUser = new User();
        newUser.setUsername("existing_" + timestamp); // 相同用户名
        newUser.setPassword("123456");
        newUser.setEmail("different_" + timestamp + "@example.com"); // 不同邮箱
        newUser.setRealName("新用户");

        // When
        boolean result = userService.register(newUser);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("SERVICE-003: 注册失败-邮箱已存在")
    void testRegisterFailureEmailExists() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User existingUser = new User();
        existingUser.setUsername("existing_" + timestamp);
        existingUser.setPassword("123456");
        existingUser.setEmail("existing_" + timestamp + "@example.com");
        existingUser.setRealName("已存在用户");
        
        // 先注册一个用户
        userService.register(existingUser);
        
        // 尝试注册相同邮箱的用户
        User newUser = new User();
        newUser.setUsername("different_" + timestamp); // 不同用户名
        newUser.setPassword("123456");
        newUser.setEmail("existing_" + timestamp + "@example.com"); // 相同邮箱
        newUser.setRealName("新用户");

        // When
        boolean result = userService.register(newUser);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("SERVICE-004: 根据用户名查询用户")
    void testGetByUsername() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("querytest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("querytest_" + timestamp + "@example.com");
        newUser.setRealName("查询测试用户");
        
        userService.register(newUser);

        // When
        User result = userService.getByUsername("querytest_" + timestamp);

        // Then
        assertNotNull(result);
        assertEquals("querytest_" + timestamp, result.getUsername());
        assertEquals("querytest_" + timestamp + "@example.com", result.getEmail());
    }

    @Test
    @DisplayName("SERVICE-005: 根据用户名查询用户-用户不存在")
    void testGetByUsernameNotFound() {
        // When
        User result = userService.getByUsername("nonexistent_user_12345");

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("SERVICE-006: 根据邮箱查询用户")
    void testGetByEmail() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("emailtest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("emailtest_" + timestamp + "@example.com");
        newUser.setRealName("邮箱查询测试用户");
        
        userService.register(newUser);

        // When
        User result = userService.getByEmail("emailtest_" + timestamp + "@example.com");

        // Then
        assertNotNull(result);
        assertEquals("emailtest_" + timestamp + "@example.com", result.getEmail());
        assertEquals("emailtest_" + timestamp, result.getUsername());
    }

    @Test
    @DisplayName("SERVICE-007: 用户名登录成功")
    void testLoginWithUsernameSuccess() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("logintest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("logintest_" + timestamp + "@example.com");
        newUser.setRealName("登录测试用户");

        userService.register(newUser);

        // When - 使用用户名登录
        User result = userService.login("logintest_" + timestamp, "123456");

        // Then
        assertNotNull(result);
        assertEquals("logintest_" + timestamp, result.getUsername());
    }

    @Test
    @DisplayName("SERVICE-007-2: 邮箱登录成功")
    void testLoginWithEmailSuccess() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("emaillogintest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("emaillogintest_" + timestamp + "@example.com");
        newUser.setRealName("邮箱登录测试用户");

        userService.register(newUser);

        // When - 使用邮箱登录
        User result = userService.login("emaillogintest_" + timestamp + "@example.com", "123456");

        // Then
        assertNotNull(result);
        assertEquals("emaillogintest_" + timestamp, result.getUsername());
        assertEquals("emaillogintest_" + timestamp + "@example.com", result.getEmail());
    }

    @Test
    @DisplayName("SERVICE-008: 用户名登录失败-密码错误")
    void testLoginWithUsernameFailureWrongPassword() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("logintest2_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("logintest2_" + timestamp + "@example.com");
        newUser.setRealName("登录测试用户2");

        userService.register(newUser);

        // When - 使用用户名和错误密码登录
        User result = userService.login("logintest2_" + timestamp, "wrongpassword");

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("SERVICE-008-2: 邮箱登录失败-密码错误")
    void testLoginWithEmailFailureWrongPassword() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("emaillogintest2_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("emaillogintest2_" + timestamp + "@example.com");
        newUser.setRealName("邮箱登录测试用户2");

        userService.register(newUser);

        // When - 使用邮箱和错误密码登录
        User result = userService.login("emaillogintest2_" + timestamp + "@example.com", "wrongpassword");

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("SERVICE-009: 用户登录失败-用户不存在")
    void testLoginFailureUserNotFound() {
        // When
        User result = userService.login("nonexistent_user_12345", "123456");

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("SERVICE-010: 检查用户名是否存在")
    void testExistsByUsername() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("existstest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("existstest_" + timestamp + "@example.com");
        newUser.setRealName("存在性测试用户");
        
        userService.register(newUser);

        // When & Then
        assertTrue(userService.existsByUsername("existstest_" + timestamp));
        assertFalse(userService.existsByUsername("nonexistent_user_12345"));
    }

    @Test
    @DisplayName("SERVICE-011: 检查邮箱是否存在")
    void testExistsByEmail() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("emailexists_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("emailexists_" + timestamp + "@example.com");
        newUser.setRealName("邮箱存在性测试用户");
        
        userService.register(newUser);

        // When & Then
        assertTrue(userService.existsByEmail("emailexists_" + timestamp + "@example.com"));
        assertFalse(userService.existsByEmail("<EMAIL>"));
    }

    @Test
    @DisplayName("SERVICE-012: 更新用户信息")
    void testUpdateUser() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("updatetest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("updatetest_" + timestamp + "@example.com");
        newUser.setRealName("更新测试用户");
        
        userService.register(newUser);
        
        // 修改用户信息
        newUser.setRealName("更新后的用户名");
        newUser.setPhone("13800138000");

        // When
        boolean result = userService.updateById(newUser);

        // Then
        assertTrue(result);
        
        // 验证更新结果
        User updatedUser = userService.getByUsername("updatetest_" + timestamp);
        assertEquals("更新后的用户名", updatedUser.getRealName());
        assertEquals("13800138000", updatedUser.getPhone());
    }

    @Test
    @DisplayName("SERVICE-013: 删除用户")
    void testDeleteUser() {
        // Given - 先创建一个用户
        String timestamp = String.valueOf(System.currentTimeMillis());
        User newUser = new User();
        newUser.setUsername("deletetest_" + timestamp);
        newUser.setPassword("123456");
        newUser.setEmail("deletetest_" + timestamp + "@example.com");
        newUser.setRealName("删除测试用户");
        
        userService.register(newUser);
        Long userId = newUser.getId();

        // When
        boolean result = userService.removeById(userId);

        // Then
        assertTrue(result);
        
        // 验证用户已删除
        User deletedUser = userService.getById(userId);
        assertNull(deletedUser);
    }
}
