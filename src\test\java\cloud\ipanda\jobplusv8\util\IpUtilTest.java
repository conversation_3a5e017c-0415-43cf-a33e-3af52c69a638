package cloud.ipanda.jobplusv8.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * IP工具类测试
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@ActiveProfiles("test")
@DisplayName("IP工具类测试")
class IpUtilTest {

    @Test
    @DisplayName("UTIL-001: 获取普通IP地址")
    void testGetClientIpNormal() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("UTIL-002: 获取X-Forwarded-For头中的IP")
    void testGetClientIpFromXForwardedFor() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("X-Forwarded-For", "***********, *************");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("UTIL-003: 获取X-Real-IP头中的IP")
    void testGetClientIpFromXRealIp() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("X-Real-IP", "***********");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("UTIL-004: 获取Proxy-Client-IP头中的IP")
    void testGetClientIpFromProxyClientIp() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("Proxy-Client-IP", "***********");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("UTIL-005: 获取WL-Proxy-Client-IP头中的IP")
    void testGetClientIpFromWLProxyClientIp() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("WL-Proxy-Client-IP", "***********");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("UTIL-006: 处理unknown值")
    void testGetClientIpWithUnknown() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("X-Forwarded-For", "unknown");
        request.addHeader("X-Real-IP", "***********");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("UTIL-007: 处理空值")
    void testGetClientIpWithEmpty() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("X-Forwarded-For", "");
        request.addHeader("X-Real-IP", "***********");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("UTIL-008: 处理IPv6本地地址")
    void testGetClientIpWithIPv6Localhost() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("0:0:0:0:0:0:0:1");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("127.0.0.1", ip);
    }

    @Test
    @DisplayName("UTIL-009: 处理IPv4本地地址")
    void testGetClientIpWithIPv4Localhost() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("127.0.0.1");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("127.0.0.1", ip);
    }

    @Test
    @DisplayName("UTIL-010: 识别Windows操作系统")
    void testGetOperatingSystemWindows() {
        // Given
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

        // When
        String os = IpUtil.getOperatingSystem(userAgent);

        // Then
        assertEquals("Windows", os);
    }

    @Test
    @DisplayName("UTIL-011: 识别Mac操作系统")
    void testGetOperatingSystemMac() {
        // Given
        String userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36";

        // When
        String os = IpUtil.getOperatingSystem(userAgent);

        // Then
        assertEquals("Mac OS", os);
    }

    @Test
    @DisplayName("UTIL-012: 识别Linux操作系统")
    void testGetOperatingSystemLinux() {
        // Given
        String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36";

        // When
        String os = IpUtil.getOperatingSystem(userAgent);

        // Then
        assertEquals("Linux", os);
    }

    @Test
    @DisplayName("UTIL-013: 识别Android操作系统")
    void testGetOperatingSystemAndroid() {
        // Given
        String userAgent = "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36";

        // When
        String os = IpUtil.getOperatingSystem(userAgent);

        // Then
        assertEquals("Android", os);
    }

    @Test
    @DisplayName("UTIL-014: 识别iOS操作系统")
    void testGetOperatingSystemIOS() {
        // Given
        String userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15";

        // When
        String os = IpUtil.getOperatingSystem(userAgent);

        // Then
        assertEquals("iOS", os);
    }

    @Test
    @DisplayName("UTIL-015: 识别Chrome浏览器")
    void testGetBrowserChrome() {
        // Given
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";

        // When
        String browser = IpUtil.getBrowser(userAgent);

        // Then
        assertEquals("Chrome", browser);
    }

    @Test
    @DisplayName("UTIL-016: 识别Firefox浏览器")
    void testGetBrowserFirefox() {
        // Given
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0";

        // When
        String browser = IpUtil.getBrowser(userAgent);

        // Then
        assertEquals("Firefox", browser);
    }

    @Test
    @DisplayName("UTIL-017: 识别Safari浏览器")
    void testGetBrowserSafari() {
        // Given
        String userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15";

        // When
        String browser = IpUtil.getBrowser(userAgent);

        // Then
        assertEquals("Safari", browser);
    }

    @Test
    @DisplayName("UTIL-018: 识别Edge浏览器")
    void testGetBrowserEdge() {
        // Given
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59";

        // When
        String browser = IpUtil.getBrowser(userAgent);

        // Then
        assertEquals("Edge", browser);
    }

    @Test
    @DisplayName("UTIL-019: 处理未知操作系统")
    void testGetOperatingSystemUnknown() {
        // Given
        String userAgent = "SomeUnknownUserAgent/1.0";

        // When
        String os = IpUtil.getOperatingSystem(userAgent);

        // Then
        assertEquals("Unknown", os);
    }

    @Test
    @DisplayName("UTIL-020: 处理未知浏览器")
    void testGetBrowserUnknown() {
        // Given
        String userAgent = "SomeUnknownBrowser/1.0";

        // When
        String browser = IpUtil.getBrowser(userAgent);

        // Then
        assertEquals("Unknown", browser);
    }

    @Test
    @DisplayName("UTIL-021: 处理空User-Agent")
    void testGetOperatingSystemWithNullUserAgent() {
        // When
        String os = IpUtil.getOperatingSystem(null);
        String browser = IpUtil.getBrowser(null);

        // Then
        assertEquals("Unknown", os);
        assertEquals("Unknown", browser);
    }

    @Test
    @DisplayName("UTIL-022: 处理空请求")
    void testGetClientIpWithNullRequest() {
        // When
        String ip = IpUtil.getClientIp(null);

        // Then
        assertEquals("unknown", ip);
    }

    @Test
    @DisplayName("UTIL-023: 多个代理IP处理")
    void testGetClientIpWithMultipleProxies() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("X-Forwarded-For", "***********, ***********, *************");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip); // 应该返回第一个IP
    }

    @Test
    @DisplayName("UTIL-024: 优先级测试")
    void testGetClientIpPriority() {
        // Given - 设置多个头部，测试优先级
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        request.addHeader("X-Forwarded-For", "***********");
        request.addHeader("X-Real-IP", "***********");
        request.addHeader("Proxy-Client-IP", "***********");
        request.addHeader("WL-Proxy-Client-IP", "***********");

        // When
        String ip = IpUtil.getClientIp(request);

        // Then
        assertEquals("***********", ip); // X-Forwarded-For 优先级最高
    }
}
