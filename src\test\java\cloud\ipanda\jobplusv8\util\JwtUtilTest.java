package cloud.ipanda.jobplusv8.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT工具类测试
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
class JwtUtilTest {

    @InjectMocks
    private JwtUtil jwtUtil;

    private String testSecret = "jobplusv8-secret-key-for-jwt-token-generation-and-validation-must-be-at-least-256-bits";
    private Long testExpiration = 86400000L; // 24小时

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(jwtUtil, "secret", testSecret);
        ReflectionTestUtils.setField(jwtUtil, "expiration", testExpiration);
    }

    @Test
    void testGenerateToken() {
        // 准备测试数据
        Long userId = 1L;
        String username = "testuser";

        // 执行测试
        String token = jwtUtil.generateToken(userId, username);

        // 验证结果
        assertNotNull(token);
        assertTrue(token.length() > 0);
        
        // 验证token中的信息
        assertEquals(username, jwtUtil.getUsernameFromToken(token));
        assertEquals(userId, jwtUtil.getUserIdFromToken(token));
    }

    @Test
    void testGetTokenFromRequest_ValidBearerToken() {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();
        String token = "eyJhbGciOiJIUzUxMiJ9.test.token";
        request.addHeader("Authorization", "Bearer " + token);

        // 执行测试
        String extractedToken = jwtUtil.getTokenFromRequest(request);

        // 验证结果
        assertEquals(token, extractedToken);
    }

    @Test
    void testGetTokenFromRequest_NoAuthorizationHeader() {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();

        // 执行测试
        String extractedToken = jwtUtil.getTokenFromRequest(request);

        // 验证结果
        assertNull(extractedToken);
    }

    @Test
    void testGetTokenFromRequest_InvalidBearerFormat() {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("Authorization", "Basic dGVzdDp0ZXN0");

        // 执行测试
        String extractedToken = jwtUtil.getTokenFromRequest(request);

        // 验证结果
        assertNull(extractedToken);
    }

    @Test
    void testGetTokenFromRequest_EmptyBearerToken() {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("Authorization", "Bearer ");

        // 执行测试
        String extractedToken = jwtUtil.getTokenFromRequest(request);

        // 验证结果
        assertNull(extractedToken);
    }

    @Test
    void testGetCurrentUserIdFromRequest_ValidToken() {
        // 准备测试数据
        Long userId = 123L;
        String username = "testuser";
        String token = jwtUtil.generateToken(userId, username);
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("Authorization", "Bearer " + token);

        // 执行测试
        Long extractedUserId = jwtUtil.getCurrentUserIdFromRequest(request);

        // 验证结果
        assertEquals(userId, extractedUserId);
    }

    @Test
    void testGetCurrentUserIdFromRequest_NoToken() {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();

        // 执行测试
        Long extractedUserId = jwtUtil.getCurrentUserIdFromRequest(request);

        // 验证结果
        assertNull(extractedUserId);
    }

    @Test
    void testGetCurrentUserIdFromRequest_InvalidToken() {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("Authorization", "Bearer invalid.token.here");

        // 执行测试
        Long extractedUserId = jwtUtil.getCurrentUserIdFromRequest(request);

        // 验证结果
        assertNull(extractedUserId);
    }

    @Test
    void testValidateToken_ValidToken() {
        // 准备测试数据
        Long userId = 1L;
        String username = "testuser";
        String token = jwtUtil.generateToken(userId, username);

        // 执行测试
        Boolean isValid = jwtUtil.validateToken(token, username);

        // 验证结果
        assertTrue(isValid);
    }

    @Test
    void testValidateToken_WrongUsername() {
        // 准备测试数据
        Long userId = 1L;
        String username = "testuser";
        String wrongUsername = "wronguser";
        String token = jwtUtil.generateToken(userId, username);

        // 执行测试
        Boolean isValid = jwtUtil.validateToken(token, wrongUsername);

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testValidateToken_InvalidToken() {
        // 准备测试数据
        String invalidToken = "invalid.token.here";
        String username = "testuser";

        // 执行测试
        Boolean isValid = jwtUtil.validateToken(invalidToken, username);

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testIsTokenExpired_ValidToken() {
        // 准备测试数据
        Long userId = 1L;
        String username = "testuser";
        String token = jwtUtil.generateToken(userId, username);

        // 执行测试
        Boolean isExpired = jwtUtil.isTokenExpired(token);

        // 验证结果
        assertFalse(isExpired);
    }

    @Test
    void testGetExpirationDateFromToken() {
        // 准备测试数据
        Long userId = 1L;
        String username = "testuser";
        String token = jwtUtil.generateToken(userId, username);

        // 执行测试
        java.util.Date expirationDate = jwtUtil.getExpirationDateFromToken(token);

        // 验证结果
        assertNotNull(expirationDate);
        assertTrue(expirationDate.after(new java.util.Date()));
    }
}
