package cloud.ipanda.jobplusv8.websocket;

import cloud.ipanda.jobplusv8.entity.User;
import cloud.ipanda.jobplusv8.service.AudioFileService;
import cloud.ipanda.jobplusv8.service.UserService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import javax.websocket.Session;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 腾讯语音识别WebSocket测试
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
class TencentInterviewWebsocketTest {

    @Mock
    private ApplicationContext applicationContext;
    
    @Mock
    private JwtUtil jwtUtil;
    
    @Mock
    private UserService userService;
    
    @Mock
    private SpeechSessionService speechSessionService;
    
    @Mock
    private AudioFileService audioFileService;
    
    @Mock
    private Session session;

    private TencentInterviewWebsocket websocket;
    private User testUser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置ApplicationContext
        TencentInterviewWebsocket.setApplicationContext(applicationContext);
        
        // 模拟Spring Bean
        when(applicationContext.getBean(JwtUtil.class)).thenReturn(jwtUtil);
        when(applicationContext.getBean(UserService.class)).thenReturn(userService);
        when(applicationContext.getBean(SpeechSessionService.class)).thenReturn(speechSessionService);
        when(applicationContext.getBean(AudioFileService.class)).thenReturn(audioFileService);
        
        // 创建测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setSpeechDuration(3600L); // 1小时
        testUser.setSpeechUsedDuration(1800L); // 已用30分钟
        testUser.setStatus(1);
        
        // 模拟Session
        when(session.getId()).thenReturn("test-session-id");
        when(session.isOpen()).thenReturn(true);
        
        websocket = new TencentInterviewWebsocket();
    }

    @Test
    @DisplayName("测试WebSocket连接建立")
    void testOnOpen() throws Exception {
        // When
        websocket.onOpen(session);
        
        // Then
        assertEquals(1, TencentInterviewWebsocket.getActiveSessionCount());
    }

    @Test
    @DisplayName("测试认证成功流程")
    void testAuthenticationSuccess() {
        // Given
        String token = "valid-jwt-token";
        String username = "testuser";
        
        when(jwtUtil.getUsernameFromToken(token)).thenReturn(username);
        when(jwtUtil.validateToken(token, username)).thenReturn(true);
        when(userService.getByUsername(username)).thenReturn(testUser);
        when(speechSessionService.hasEnoughDuration(1L)).thenReturn(true);
        when(speechSessionService.getRemainingDuration(1L)).thenReturn(1800L);
        
        // 模拟会话创建
        // when(speechSessionService.createSession(any(), anyString(), anyString(), anyString()))
        //     .thenReturn(createMockSpeechSession());
        
        String authMessage = "{\"type\":\"AUTH\",\"token\":\"" + token + "\"}";
        
        // When
        websocket.onMessage(authMessage, session);
        
        // Then
        verify(jwtUtil).getUsernameFromToken(token);
        verify(jwtUtil).validateToken(token, username);
        verify(userService).getByUsername(username);
        verify(speechSessionService).hasEnoughDuration(1L);
    }

    @Test
    @DisplayName("测试认证失败 - 无效令牌")
    void testAuthenticationFailure_InvalidToken() {
        // Given
        String token = "invalid-jwt-token";
        
        when(jwtUtil.getUsernameFromToken(token)).thenReturn(null);
        
        String authMessage = "{\"type\":\"AUTH\",\"token\":\"" + token + "\"}";
        
        // When
        websocket.onMessage(authMessage, session);
        
        // Then
        verify(jwtUtil).getUsernameFromToken(token);
        verify(userService, never()).getByUsername(anyString());
    }

    @Test
    @DisplayName("测试认证失败 - 用户不存在")
    void testAuthenticationFailure_UserNotFound() {
        // Given
        String token = "valid-jwt-token";
        String username = "nonexistent";
        
        when(jwtUtil.getUsernameFromToken(token)).thenReturn(username);
        when(jwtUtil.validateToken(token, username)).thenReturn(true);
        when(userService.getByUsername(username)).thenReturn(null);
        
        String authMessage = "{\"type\":\"AUTH\",\"token\":\"" + token + "\"}";
        
        // When
        websocket.onMessage(authMessage, session);
        
        // Then
        verify(userService).getByUsername(username);
        verify(speechSessionService, never()).hasEnoughDuration(anyLong());
    }

    @Test
    @DisplayName("测试认证失败 - 时长不足")
    void testAuthenticationFailure_InsufficientDuration() {
        // Given
        String token = "valid-jwt-token";
        String username = "testuser";
        
        when(jwtUtil.getUsernameFromToken(token)).thenReturn(username);
        when(jwtUtil.validateToken(token, username)).thenReturn(true);
        when(userService.getByUsername(username)).thenReturn(testUser);
        when(speechSessionService.hasEnoughDuration(1L)).thenReturn(false);
        when(speechSessionService.getRemainingDuration(1L)).thenReturn(0L);
        
        String authMessage = "{\"type\":\"AUTH\",\"token\":\"" + token + "\"}";
        
        // When
        websocket.onMessage(authMessage, session);
        
        // Then
        verify(speechSessionService).hasEnoughDuration(1L);
        verify(speechSessionService).getRemainingDuration(1L);
    }

    @Test
    @DisplayName("测试未知消息类型")
    void testUnknownMessageType() {
        // Given
        String unknownMessage = "{\"type\":\"UNKNOWN_TYPE\"}";
        
        // When
        websocket.onMessage(unknownMessage, session);
        
        // Then - 应该发送错误消息，但由于我们没有模拟sendMessage，这里只是验证不会抛异常
        assertDoesNotThrow(() -> websocket.onMessage(unknownMessage, session));
    }

    @Test
    @DisplayName("测试JSON解析错误")
    void testInvalidJsonMessage() {
        // Given
        String invalidJson = "invalid json";
        
        // When & Then
        assertDoesNotThrow(() -> websocket.onMessage(invalidJson, session));
    }

    @Test
    @DisplayName("测试获取活跃会话数量")
    void testGetActiveSessionCount() {
        // Given
        int initialCount = TencentInterviewWebsocket.getActiveSessionCount();
        
        // When
        try {
            websocket.onOpen(session);
            
            // Then
            assertEquals(initialCount + 1, TencentInterviewWebsocket.getActiveSessionCount());
        } catch (Exception e) {
            fail("onOpen should not throw exception");
        }
    }

    @Test
    @DisplayName("测试强制关闭用户会话")
    void testForceCloseUserSessions() {
        // Given
        Long userId = 1L;
        String reason = "管理员强制结束";
        
        // When
        TencentInterviewWebsocket.forceCloseUserSessions(userId, reason);
        
        // Then - 验证方法调用不会抛异常
        assertDoesNotThrow(() -> TencentInterviewWebsocket.forceCloseUserSessions(userId, reason));
    }

    @Test
    @DisplayName("测试WebSocket连接关闭")
    void testOnClose() {
        // When & Then
        assertDoesNotThrow(() -> websocket.onClose());
    }

    @Test
    @DisplayName("测试WebSocket连接错误")
    void testOnError() {
        // Given
        Exception testException = new RuntimeException("Test error");
        
        // When & Then
        assertDoesNotThrow(() -> websocket.onError(session, testException));
    }

    /**
     * 创建模拟的语音会话对象
     */
    // private SpeechSession createMockSpeechSession() {
    //     SpeechSession session = new SpeechSession();
    //     session.setId(1L);
    //     session.setSessionId("test-speech-session-id");
    //     session.setUserId(1L);
    //     session.setUsername("testuser");
    //     session.setStatus(SpeechSession.Status.ACTIVE);
    //     return session;
    // }
}
