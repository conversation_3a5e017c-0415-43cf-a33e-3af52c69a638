# 测试环境配置
server:
  port: 8080

spring:
  main:
    allow-circular-references: false
  
  # 数据库配置 - 使用内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # Redis配置 - 测试环境可以使用嵌入式Redis或跳过
  redis:
    host: 127.0.0.1
    port: 6379
    password: 
    database: 1
    timeout: 5000ms
    
  # 邮件配置 - 测试环境禁用实际发送
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: test
    from: <EMAIL>
    from-name: JobPlusV8测试
    code-expire-minutes: 5
    code-length: 6
    send-interval-seconds: 60

# JWT配置
jwt:
  secret: test-secret-key-for-jwt-token-generation-and-validation-must-be-at-least-256-bits
  expiration: 86400000

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false

# 日志配置
logging:
  level:
    cloud.ipanda.jobplusv8: DEBUG
    org.springframework.security: DEBUG
